name: CI Tests

on:
  pull_request:
    branches:
      - ua-merge
      - development
      - main
  push:
    branches:
      - ua-merge
      - development
      - main

jobs:
  test:
    name: Run Tests
    runs-on: blacksmith-4vcpu-ubuntu-2404
    timeout-minutes: 30

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: useblacksmith/setup-node@v5
        with:
          node-version: 22
          cache: 'yarn'

      - name: Install dependencies
        run: yarn install --frozen-lockfile

      - name: Setup test database
        run: |
          docker compose down
          docker compose up -d

      - name: Wait for database to be ready
        run: yarn pretest:wait-for-db

      - name: Setup prisma client and push schema
        run: |
          yarn generate
          yarn turbo run db:push:test

      - name: Run tests
        run: yarn test:ci
