services:
  mysql:
    image: mysql:8.0
    container_name: memberup-mysql-test
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: memberup_test
      MYSQL_USER: memberup
      MYSQL_PASSWORD: memberup_password
    ports:
      - '3307:3306'
    volumes:
      - mysql_db_test:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    healthcheck:
      test: ['CMD', 'mysqladmin', 'ping', '-h', 'localhost']
      timeout: 20s
      retries: 10

volumes:
  mysql_db_test:
    driver: local
