import { execSync } from 'child_process'

const MAX_RETRIES = 30
const INTERVAL_MS = 2000

for (let i = 0; i < MAX_RETRIES; i++) {
  try {
    execSync('docker exec memberup-mysql-test mysqladmin ping -h localhost -uroot -prootpassword --silent', {
      stdio: 'ignore',
    })
    console.log('✅ MySQL is ready')
    // wait for 5 more seconds to ensure the database is ready
    Atomics.wait(new Int32Array(new SharedArrayBuffer(4)), 0, 0, 5000)
    process.exit(0)
  } catch (err) {
    console.log(`⏳ Waiting for MySQL... (${i + 1}/${MAX_RETRIES})`)
    Atomics.wait(new Int32Array(new SharedArrayBuffer(4)), 0, 0, INTERVAL_MS)
  }
}

console.error('❌ MySQL did not start in time')
process.exit(1)
