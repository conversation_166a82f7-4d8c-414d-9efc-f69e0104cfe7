import { I<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from '@mui/material'
import useTheme from '@mui/material/styles/useTheme'
import { useEffect, useRef, useState } from 'react'

import SVGEmojiNew from '../../svgs/emoji-new'
import EmojiMartPicker from './emoji-mart-picker'
import { useStore } from '@/hooks/useStore'

const EmojiPickerWithButton = ({
  className,
  onSelectedEmoji,
}: {
  className?: string
  onSelectedEmoji: (e) => void
}) => {
  const themeMode = useStore((state) => state.ui.themeMode)
  const [emojiPickerAnchor, setEmojiPickerAnchor] = useState(null)
  const wrapperRef = useRef(null)
  const buttonRef = useRef(null)

  // Use the 'open' state to manage the visibility of the picker
  const [open, setOpen] = useState(false)

  const handleOpenClosePicker = () => {
    setOpen(!open)
    setEmojiPickerAnchor((anchor) => (anchor ? null : buttonRef.current)) // Toggle anchor
  }

  useEffect(() => {
    function handleClickOutside(event) {
      if (buttonRef.current && buttonRef.current.contains(event.target)) {
        return
      }

      if (wrapperRef.current && !wrapperRef.current.contains(event.target)) {
        setOpen(false)
        setEmojiPickerAnchor(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  return (
    <>
      <IconButton className={className} onClick={handleOpenClosePicker} ref={buttonRef}>
        <SVGEmojiNew />
      </IconButton>
      {open && (
        <Popper
          open={Boolean(emojiPickerAnchor)}
          anchorEl={emojiPickerAnchor}
          placement="bottom-start"
          modifiers={[
            {
              name: 'offset',
              options: {
                offset: [0, 8],
              },
            },
          ]}
          style={{ zIndex: 2000 }}
        >
          <div className="emoji-picker-wrapper" ref={wrapperRef}>
            <EmojiMartPicker
              onPickerClose={() => {
                setOpen(false)
                setEmojiPickerAnchor(null)
              }}
              onEmojiSelect={(e) => {
                onSelectedEmoji(e)
                setOpen(false)
                setEmojiPickerAnchor(null)
              }}
              theme={themeMode}
            />
          </div>
        </Popper>
      )}
    </>
  )
}

export default EmojiPickerWithButton
