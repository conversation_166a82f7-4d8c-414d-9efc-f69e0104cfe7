import CancelIcon from '@mui/icons-material/Cancel'
import CircularProgress from '@mui/material/CircularProgress'
import IconButton from '@mui/material/IconButton'
import Stack from '@mui/material/Stack'
import { makeStyles } from '@mui/styles'
import { useEffect, useState } from 'react'
import ReactPlayer from 'react-player/lazy'

import { getURLDataApi } from '@memberup/shared/src/services/apis/unfurl.api'
import LinkPreview from '@/components/feed/link-preview'

const useStyles = makeStyles((theme) => ({
  player: {
    borderRadius: '16px',
    overflow: 'hidden',
    marginBottom: '8px',
  },
}))

type LinkData = {
  url: string
  title: string
  description: string
  thumbnail: string
}

export default function EditPostLinkPreview({ url, onClose, onLoad }) {
  const [linkData, setLinkData] = useState({} as LinkData)
  const [isLoading, setIsLoading] = useState(true)
  const [isError, setIsError] = useState(false)

  useEffect(() => {
    ;(async function () {
      setIsLoading(true)

      try {
        const res = await getURLDataApi(url)
        if (!res.data.success) {
          setIsError(true)
          setIsLoading(false)
        } else {
          const allLinkData = res.data.data
          if (onLoad) {
            onLoad(res.data)
          }

          const linkData = {
            url: url,
            title: allLinkData.title,
            description: allLinkData.description,
            thumbnail: allLinkData?.favicon || allLinkData?.imageSrc?.url || allLinkData?.imageSrc,
          }
          setLinkData(linkData)
          setIsLoading(false)
        }
      } catch (e) {
        setIsError(true)
        setIsLoading(false)
      }
    })()
  }, [])

  const classes = useStyles()

  const handleCloseClick = () => {
    if (onClose) {
      onClose()
    }
  }

  return (
    <Stack
      sx={{
        borderRadius: '16px',
        border: 'solid #25262b 1px',
        alignContent: 'center',
        display: 'flex',
        position: 'relative',
        marginBottom: '8px',
        padding: '8px',
        height: ReactPlayer.canPlay(url) ? '325px' : '145px',
      }}
    >
      <IconButton
        sx={{ position: 'absolute', right: '8px', top: '10px' }}
        size="small"
        aria-label="remove link preview"
        onClick={handleCloseClick}
      >
        <CancelIcon fontSize="small" />
      </IconButton>
      {isError && <LinkPreview disabled={true} url={url} title={null} description={null} thumbnail={null} />}
      {isLoading && <CircularProgress sx={{ textAlign: 'center', margin: 'auto' }} size={16} />}
      {!isError &&
        !isLoading &&
        (ReactPlayer.canPlay(url) ? (
          <ReactPlayer width={'100%'} className={classes.player} light={true} controls={true} url={url} />
        ) : (
          // TODO: Should we show a loading for images?
          <LinkPreview
            disabled={true}
            url={url}
            title={linkData.title}
            description={linkData.description}
            thumbnail={linkData.thumbnail}
          />
        ))}
    </Stack>
  )
}
