import Button from '@mui/material/Button'
import CircularProgress from '@mui/material/CircularProgress'
import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import { useTheme } from '@mui/styles'
import { Elements, PaymentElement, useElements, useStripe } from '@stripe/react-stripe-js'
import { loadStripe, PaymentMethod } from '@stripe/stripe-js'
import { useEffect, useMemo, useState } from 'react'
import { toast } from 'react-toastify'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { STRIPE_PUBLISH_KEY } from '@memberup/shared/src/config/envs'
import { adjustRGBA } from '@memberup/shared/src/libs/color'
import { showToast } from '@memberup/shared/src/libs/toast'
import { attachStripePaymentMethodApi, createStripeSetupIntentApi } from '@memberup/shared/src/services/apis/stripe.api'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { getStripeSecureReturnUrl } from '@/lib/stripe'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const stripePromise = loadStripe(STRIPE_PUBLISH_KEY)

interface Props {
  onSuccess: (paymentMethod?: PaymentMethod) => void
  onCancel: () => void
  isMembership?: boolean
}

const PaymentMethodForm = ({ onSuccess, onCancel }: Props) => {
  const mountedRef = useMounted(true)
  const theme = useTheme()
  const [loading, setLoading] = useState(false)
  const stripe = useStripe()
  const elements = useElements()
  const handleFormSubmit = async (e: React.FormEvent) => {
    try {
      e.preventDefault()
      setLoading(true)

      const { setupIntent, error } = await stripe.confirmSetup({
        elements,
        redirect: 'if_required',
        confirmParams: {
          expand: ['payment_method'],
          return_url: getStripeSecureReturnUrl(),
        },
      })

      if (error) {
        showToast(error.message || 'An unexpected error occurred.', 'warning')
        setLoading(false)
      } else {
        if (mountedRef.current) {
          setLoading(true)
          const paymentMethod = setupIntent?.payment_method as PaymentMethod
          if (paymentMethod) {
            onSuccess(paymentMethod)
          }
        }
      }
    } catch (err: any) {
      console.log(err.message)
      setLoading(false)
      if (err?.message) {
        showToast(err.message, 'error')
      }
    }
  }

  return (
    <form autoComplete="off" onSubmit={handleFormSubmit}>
      <PaymentElement />
      <Stack direction="row" gap={3} sx={{ mt: 3 }}>
        <Button
          className="round-small"
          sx={{ backgroundColor: adjustRGBA(theme.palette.primary.main, 0.8) }}
          variant="contained"
          color="primary"
          type="submit"
          fullWidth
          disabled={loading || !stripe || !elements}
        >
          {loading ? <CircularProgress size={16} /> : 'Update'}
        </Button>
        <Button
          className="round-small"
          //color="inherit"
          sx={{
            color: theme.palette.mode === 'dark' ? '#fff' : '#000',
            borderColor: theme.palette.mode === 'dark' ? 'initial' : '#000',
          }}
          fullWidth
          variant="outlined"
          onClick={() => onCancel()}
        >
          Cancel
        </Button>
      </Stack>
    </form>
  )
}

const EditPaymentMethod = ({ onSuccess, onCancel, isMembership = false }: Props) => {
  const membershipSetting = useAppSelector(selectMembershipSetting)
  const [clientSecret, setClientSecret] = useState('')

  useEffect(() => {
    const fetchPaymenIntent = async () => {
      const intentRes = await createStripeSetupIntentApi(isMembership, {
        mode: 'setup',
        payment_method_types: ['card'],
      })

      setClientSecret(intentRes.data.data?.['client_secret'])
    }

    fetchPaymenIntent()
  }, [])

  const stripePromise = useMemo(() => {
    if (isMembership) {
      if (
        !membershipSetting?.stripe_connect_account?.stripe_publishable_key &&
        !membershipSetting?.stripe_connect_account?.enabled
      )
        return null

      if (membershipSetting?.stripe_connect_account?.enabled) {
        return loadStripe(STRIPE_PUBLISH_KEY, {
          stripeAccount: membershipSetting?.stripe_connect_account.stripe_user_id,
        })
      }
      return loadStripe(membershipSetting?.stripe_connect_account?.stripe_publishable_key)
    }
    return loadStripe(STRIPE_PUBLISH_KEY)
  }, [membershipSetting?.stripe_connect_account?.stripe_publishable_key])

  const handleSuccess = (pm: PaymentMethod) => {
    attachStripePaymentMethodApi(isMembership, pm.id)
      .then((res) => {
        if (res.data.success) {
          showToast('Payment added', 'success')
        }
        onSuccess(pm)
      })
      .catch((err) => {
        showToast(err.response?.message || 'An unexpected error occurred.', 'warning')
      })
  }

  // TODO loading
  if (!stripePromise || !clientSecret) return <Skeleton variant="rounded" height={317} sx={{ width: '100%' }} />

  return (
    <Elements
      stripe={stripePromise}
      options={{
        clientSecret: clientSecret,
        appearance: {
          theme: membershipSetting?.theme_mode === THEME_MODE_ENUM.dark ? 'night' : 'stripe',
        },
        loader: 'always',
      }}
    >
      <PaymentMethodForm onSuccess={handleSuccess} onCancel={onCancel} />
    </Elements>
  )
}

export default EditPaymentMethod
