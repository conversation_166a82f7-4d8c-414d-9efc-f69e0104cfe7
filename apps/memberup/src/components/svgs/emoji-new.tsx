import React from 'react'

const <PERSON><PERSON><PERSON>ji<PERSON><PERSON>: React.FC<{ width?: number; height?: number }> = ({ width, height }) => {
  return (
    <svg width={width || 16} height={height || 16} viewBox="0 0 16 16" version="1.1">
      <g stroke="none" strokeWidth="1" fill="none" fillRule="evenodd">
        <g transform="translate(-2, -2)" fill="currentColor">
          <path d="M10,2 C14.418278,2 18,5.581722 18,10 C18,14.418278 14.418278,18 10,18 C5.581722,18 2,14.418278 2,10 C2,5.581722 5.581722,2 10,2 Z M10,4 C6.6862915,4 4,6.6862915 4,10 C4,13.3137085 6.6862915,16 10,16 C13.3137085,16 16,13.3137085 16,10 C16,6.6862915 13.3137085,4 10,4 Z M6.6053068,12.047916 C6.6053068,12.047916 6.6053068,12.047916 6.6053068,12.047916 C6.65538267,12.1276179 7.85626132,14 10,14 C12.1437387,14 13.3446173,12.127652 13.3946932,12.047916 C13.3946932,12.047916 13.3946932,12.047916 13.3946932,12.047916 C13.595656,11.7280051 13.4992295,11.3057535 13.1793186,11.1047907 C13.0703055,11.0363106 12.9441809,10.9999824 12.8154433,11 L7.18449323,11 C6.80672823,11.0000375 6.50048919,11.3062765 6.50048919,11.6840415 C6.50048919,11.8127802 6.5368208,11.9389054 6.6053068,12.047916 Z M8,7 C8.55228475,7 9,7.44771525 9,8 C9,8.55228475 8.55228475,9 8,9 C7.44771525,9 7,8.55228475 7,8 C7,7.44771525 7.44771525,7 8,7 Z M12,7 C12.5522847,7 13,7.44771525 13,8 C13,8.55228475 12.5522847,9 12,9 C11.4477153,9 11,8.55228475 11,8 C11,7.44771525 11.4477153,7 12,7 Z" />
        </g>
      </g>
    </svg>
  )
}

export default SVGEmojiNew
