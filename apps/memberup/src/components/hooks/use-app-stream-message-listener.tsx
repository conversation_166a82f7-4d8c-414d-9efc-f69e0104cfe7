import _debounce from 'lodash/debounce'
import _orderBy from 'lodash/orderBy'
import { useCallback, useEffect, useRef, useState } from 'react'
import { Channel, MessageFilters, PinnedMessagePaginationOptions, PinnedMessagesSort, SearchOptions } from 'stream-chat'
import { StreamMessage, useChatContext } from 'stream-chat-react'

import { useBrowserLayoutEffect } from '@memberup/shared/src/components/hooks/use-browser-layout-effect'
import { useForceUpdate } from '@memberup/shared/src/components/hooks/use-force-update'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { FEED_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { selectUser } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const useAppStreamMessageListener = (
  initialFetch: boolean,
  status: 'all' | 'pinned' | 'unpinned',
  streamChatChannel: Channel,
  streamMessage?: StreamMessage,
  filters?: MessageFilters,
  options?: SearchOptions,
  pinnedMessagePaginationOptions?: PinnedMessagePaginationOptions,
  pinnedMessagesSort?: PinnedMessagesSort,
) => {
  const mountedRef = useMounted(true)
  const dispatch = useAppDispatch()
  const user = useAppSelector((state) => selectUser(state))
  const { client: streamChatClient } = useChatContext()
  const streamChannelRef = useRef<Channel>(null)
  const requestFetchMessagesRef = useRef(true)
  const messagesRef = useRef({
    hasMore: false,
    previous: undefined,
    next: undefined,
    results: [],
  })
  const [forceUpdateValue, forceUpdate] = useForceUpdate()
  const [updatedMessage, setUpdatedMessage] = useState(null)
  const MAX_RETRIES = 5

  const fetchMessagesWithRetry = async (messageFilters, searchOptions, retries = 0) => {
    try {
      const tempResults = messagesRef.current.results || []
      const res = await streamChannelRef.current.search(messageFilters, searchOptions)
      // handle the response
      if (!mountedRef.current) return
      const temp = res.results.map((item) => item.message)
      messagesRef.current = {
        hasMore: Boolean(res.next),
        previous: res.previous,
        next: res.next,
        results: searchOptions.next ? [...tempResults, ...temp] : temp,
      }
    } catch (err) {
      if (retries < MAX_RETRIES) {
        console.log(`Attempt ${retries + 1} failed. Retrying...`)
        return fetchMessagesWithRetry(messageFilters, searchOptions, retries + 1)
      } else {
        console.log('channel search error =========', err)
      }
    } finally {
      if (mountedRef.current) {
        requestFetchMessagesRef.current = false
        forceUpdate()
      }
    }
  }

  useBrowserLayoutEffect(() => {
    streamChannelRef.current = streamChatChannel
    messagesRef.current = {
      hasMore: false,
      previous: undefined,
      next: undefined,
      results: [],
    }
  }, [streamChatChannel])

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedFetchMessages = useCallback(
    _debounce((initial?: boolean, limit?: number) => {
      if (
        !mountedRef.current ||
        !streamChannelRef.current ||
        (!initial && (!messagesRef.current.hasMore || requestFetchMessagesRef.current))
      )
        return
      requestFetchMessagesRef.current = true
      forceUpdate()

      if (status === 'pinned') {
        streamChannelRef.current
          .getPinnedMessages(pinnedMessagePaginationOptions || {}, pinnedMessagesSort)
          .then((res) => {
            if (!mountedRef.current) return
            const tempResults = messagesRef.current.results || []
            messagesRef.current = {
              hasMore: false,
              previous: null,
              next: null,
              results: _orderBy(
                pinnedMessagePaginationOptions?.offset ? [...tempResults, ...res.messages] : res.messages,
                ['pinned_at', 'created_at'],
                ['desc', 'desc'],
              ),
            }
          })
          .catch((err) => {
            console.log(err)
          })
          .finally(() => {
            if (!mountedRef.current) return
            requestFetchMessagesRef.current = false
            forceUpdate()
          })
      } else {
        const tempResults = messagesRef.current.results || []
        const messageFilters: MessageFilters = {
          ...(filters || {}),
        }
        if (streamMessage?.id) {
          messageFilters.parent_id = streamMessage.id
        }

        const searchOptions: SearchOptions = {
          limit: limit || options.limit,
        }

        if (messagesRef.current.next) {
          searchOptions.next = messagesRef.current.next
        } else {
          searchOptions.sort = options.sort
          if (tempResults.length) {
            if (searchOptions.sort?.['created_at'] === 1) {
              messageFilters.created_at = {
                $gt: tempResults[tempResults.length - 1].created_at,
              }
            } else if (searchOptions.sort?.['created_at'] === -1) {
              messageFilters.created_at = {
                $lt: tempResults[tempResults.length - 1].created_at,
              }
            }
          }
        }

        fetchMessagesWithRetry(messageFilters, searchOptions)
      }
    }, 200),
    [],
  )

  useEffect(() => {
    if (initialFetch) {
      debouncedFetchMessages(true)
    }
    return () => {}
  }, [initialFetch])

  useEffect(() => {
    let streamChatClientEventListner
    let streamChatChannelEventListener
    const isLatestTop = options?.sort?.['created_at'] === -1
    if (mountedRef.current) {
      let prevEvent: any = null
      streamChatChannelEventListener = streamChatChannel?.on((e) => {
        switch (e.type) {
          case 'app_member_updated' as any:
            if (e.updated_member) {
              const updatedMember = e.updated_member
              if ((updatedMember['teams'] || []).includes(user.membership_id)) {
                messagesRef.current.results = messagesRef.current.results.map((item) => {
                  if (item.user.id !== updatedMember['id']) return item
                  const temp = {
                    ...item,
                    user: updatedMember,
                  }
                  if (temp.latest_reactions?.length) {
                    temp.latest_reactions = temp.latest_reactions.map((item1) => {
                      if (item1.user?.id !== updatedMember['id']) return item1
                      return {
                        ...item1,
                        user: {
                          ...item1.user,
                          ...(updatedMember as any),
                        },
                      }
                    })
                  }
                  if (temp.mentioned_users?.length) {
                    temp.mentioned_users = temp.mentioned_users.map((item1) => {
                      if (item1.id !== updatedMember['id']) return item1
                      return {
                        ...item1,
                        ...(updatedMember as any),
                      }
                    })
                  }
                  return temp
                })
                forceUpdate()
              }
            }
            return
          case 'message.new':
            if (
              prevEvent?.message?.id !== e.message.id &&
              status !== 'pinned' &&
              (e.message.parent_id || undefined) === streamMessage?.id
            ) {
              prevEvent = e
              if (!messagesRef.current.next || !streamMessage) {
                const tempResults = messagesRef.current.results || []
                messagesRef.current = {
                  hasMore: streamMessage ? true : messagesRef.current.hasMore,
                  previous: streamMessage ? undefined : messagesRef.current.previous,
                  next: streamMessage ? undefined : messagesRef.current.next,
                  results: isLatestTop ? [e.message, ...tempResults] : [...tempResults, e.message],
                }
              }
              forceUpdate()
              setTimeout(() => {
                prevEvent = null
              }, 100)
            }
            return
          case 'message.updated':
            if (prevEvent?.message?.id !== e.message.id && (e.message.parent_id || undefined) === streamMessage?.id) {
              prevEvent = e
              const tempResults = messagesRef.current.results || []
              if (
                e.message.feed_status === FEED_STATUS_ENUM.reported ||
                (status === 'unpinned' && e.message.pinned) ||
                (status === 'pinned' && !e.message.pinned)
              ) {
                messagesRef.current = {
                  hasMore: messagesRef.current.hasMore,
                  previous: messagesRef.current.previous,
                  next: messagesRef.current.next,
                  results: tempResults.filter((item) => item.id !== e.message.id),
                }
                forceUpdate()
              } else {
                const tempIndex = tempResults.findIndex((item) => item.id === e.message.id)
                if (tempIndex >= 0) {
                  messagesRef.current.results = tempResults
                    .slice(0, tempIndex)
                    .concat(e.message, tempResults.slice(tempIndex + 1))
                } else {
                  if (status === 'pinned' && e.message.pinned) {
                    messagesRef.current = {
                      hasMore: messagesRef.current.hasMore,
                      previous: messagesRef.current.previous,
                      next: messagesRef.current.next,
                      results: _orderBy([...tempResults, e.message], ['pinned_at', 'created_at'], ['desc', 'desc']),
                    }
                  } else if (status === 'unpinned' && !e.message.pinned) {
                    if (tempResults.length) {
                      const lastMessage = tempResults[tempResults.length - 1]
                      const lastMessageCreatedAt = new Date(lastMessage.created_at).getTime()
                      const messageCreatedAt = new Date(e.message.created_at).getTime()
                      if (
                        (isLatestTop && lastMessageCreatedAt < messageCreatedAt) ||
                        (!isLatestTop && lastMessageCreatedAt > messageCreatedAt)
                      ) {
                        messagesRef.current = {
                          hasMore: messagesRef.current.hasMore,
                          previous: messagesRef.current.previous,
                          next: messagesRef.current.next,
                          results: _orderBy(
                            [...tempResults, e.message],
                            ['created_at'],
                            isLatestTop ? ['desc'] : ['asc'],
                          ),
                        }
                      }
                    } else {
                      messagesRef.current = {
                        hasMore: messagesRef.current.hasMore,
                        previous: messagesRef.current.previous,
                        next: messagesRef.current.next,
                        results: [e.message],
                      }
                    }
                  }
                }
                forceUpdate()
              }
              setTimeout(() => {
                prevEvent = null
              }, 100)
            }
            return
          case 'message.deleted':
            if (prevEvent?.message?.id !== e.message.id && (e.message.parent_id || undefined) === streamMessage?.id) {
              prevEvent = e
              messagesRef.current = {
                hasMore: messagesRef.current.hasMore,
                previous: messagesRef.current.previous,
                next: messagesRef.current.next,
                results: messagesRef.current.results?.filter((item) => item.id !== e.message.id) || [],
              }
              forceUpdate()
              setTimeout(() => {
                prevEvent = null
              }, 100)
            }
            return
          case 'reaction.new':
          case 'reaction.updated':
          case 'reaction.deleted':
            if (!mountedRef.current || !streamMessage?.id || e.reaction.message_id !== streamMessage.id) return
            setUpdatedMessage(e.message)
            return
        }
      })
    }

    return () => {
      streamChatClientEventListner?.unsubscribe?.()
      streamChatChannelEventListener?.unsubscribe?.()
    }
  }, [streamChatChannel])

  return {
    requestFetchMessages: requestFetchMessagesRef.current,
    messages: messagesRef.current,
    updatedMessage,
    updated: forceUpdateValue,
    fetchMessages: debouncedFetchMessages,
  }
}

export default useAppStreamMessageListener
