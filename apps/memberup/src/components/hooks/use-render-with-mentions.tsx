import parse from 'html-react-parser'
import { useMemo } from 'react'

import RenderMention from '../common/render-mention'
import { IFeed, IUser } from '@memberup/shared/src/types/interfaces'
import { unescapeSlashesAndAddLinks } from '@/memberup/libs/utils'
import { stripHtml } from '@/shared-libs/string-utils'

const EVERYONE_USER_ID = '-1'

const renderReactElements = (htmlString, members) => {
  const replaceMentionWithComponent = (node) => {
    if (node.attribs && node.attribs['data-type']) {
      const dataType = node.attribs['data-type']
      if (dataType === 'mention') {
        const userId = node.attribs['data-user-id']
        const isEveryone = userId === EVERYONE_USER_ID
        const member = members[userId] || {
          first_name: node.attribs['data-user-name'],
          last_name: '',
        }
        return <RenderMention isEveryone={isEveryone} userData={member} />
      }
    }
  }
  const reactElements = parse(htmlString, { replace: replaceMentionWithComponent })

  return reactElements
}

export function useRenderTextWithMentions(
  text,
  mentionedUsers = [],
  members: { [key: string]: IUser },
  viewType: 'condensed' | 'single' | 'comment' = 'single',
  feed: IFeed,
) {
  return useMemo(() => {
    if (viewType === 'condensed') {
      return <>{stripHtml(feed.text)}</>
    }

    if (viewType === 'single') {
      return renderReactElements(feed.text, members)
    }

    // NOTE: In the case of comments as we don't have a way to edit links yet, we just detect links and render them.
    if (viewType === 'comment') {
      return renderReactElements(unescapeSlashesAndAddLinks(feed.text), members)
    }
  }, [text, mentionedUsers, members])
}
