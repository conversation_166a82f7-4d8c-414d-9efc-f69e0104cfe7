import { useEffect, useState } from 'react'

import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { STRIPE_FREE_TRIAL_PERIOD } from '@memberup/shared/src/config/envs'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import {
  selectMembership,
  selectMembershipError,
  selectMembershipSetting,
  selectRequestGetMembership,
} from '@/memberup/store/features/membershipSlice'
import { selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppSelector } from '@/memberup/store/hooks'

const MU_ID = process.env.NEXT_PUBLIC_MU_ID

const useTrial = () => {
  const mountedRef = useMounted(true)
  const membership = useAppSelector((state) => selectMembership(state))
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const membershipError = useAppSelector((state) => selectMembershipError(state))
  const requestGetMembership = useAppSelector((state) => selectRequestGetMembership(state))
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const { isCurrentUserAdmin } = useCheckUserRole()
  const [trialDays, setTrialDays] = useState(-1000)
  const [isTrial, setIsTrial] = useState(true)

  useEffect(() => {
    if (!userProfile || !membership || !membershipSetting || requestGetMembership || membershipError) return

    if (membership.id === MU_ID || !isCurrentUserAdmin) {
      setIsTrial(false)
      return
    }

    const temp = isCurrentUserAdmin ? membershipSetting : userProfile

    if (temp?.stripe_subscription_status) {
      setIsTrial(false)
      return
    }

    let intervalId
    let trialEnd = 0

    if (temp?.stripe_subscription?.trial_end) {
      trialEnd = temp.stripe_subscription.trial_end
    } else if (temp?.createdAt) {
      trialEnd = Math.floor(new Date(temp.createdAt).getTime() / 1000) + parseInt(STRIPE_FREE_TRIAL_PERIOD as string)
    }

    const calculateTrialDays = () => {
      const now = Math.floor(new Date().getTime() / 1000)
      const days = Math.ceil((trialEnd - now) / 86400)

      if (mountedRef.current) {
        setTrialDays(days)
      }
      return days
    }

    calculateTrialDays()
    intervalId = setInterval(() => {
      const days = calculateTrialDays()

      if (days < 0) {
        clearInterval(intervalId)
      }
    }, 60000)

    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isCurrentUserAdmin,
    userProfile?.stripe_subscription,
    membership?.id,
    membershipSetting?.createdAt,
    membershipSetting?.stripe_subscription,
  ])

  return { isTrial, trialDays }
}

useTrial.displayName = 'useTrial'

export default useTrial
