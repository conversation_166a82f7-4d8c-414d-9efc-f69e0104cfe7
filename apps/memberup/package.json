{"name": "memberup", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack -p 3000", "dev:inspect": "cross-env NODE_OPTIONS='--inspect' next dev --turbopack -p 3000", "build": "prisma generate && next build --debug", "start": "next start -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "ts-lint": "tsc --noEmit --incremental", "analyze": "cross-env ANALYZE=true next build", "db:seed": "node scripts/seeds/prisma/seed.js", "stream-chat-seed": "node scripts/seeds/stream-chat-seed.js", "stream-chat-upsert-creators": "node scripts/stream-chat-upsert-creators.js", "knock-add-objects": "node scripts/knock-add-objects.js", "knock-subscribe-objects": "node scripts/knock-subscribe-objects.js", "add-library-sequences": "node scripts/add-library-sequences.js", "update-stripe-canceled-at": "node scripts/update-stripe-canceled-at.js", "update-creators-getting-started": "node scripts/update-creators-getting-started.js", "update-users-status": "node scripts/update-users-status.js", "update-user-notifications": "node scripts/update-user-notifications.js", "test": "jest -i --passWithNoTests", "test-jest:ci": "jest --ci --verbose", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build", "local-inngest": "npx inngest-cli@latest dev", "wait-for-test-db": "node scripts/wait-for-test-db.js"}, "dependencies": {"@date-io/date-fns": "^2.17.0", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@draft-js-plugins/editor": "^4.1.4", "@draft-js-plugins/emoji": "^4.6.7", "@draft-js-plugins/mention": "^5.2.2", "@draft-js-plugins/utils": "^4.2.1", "@emoji-mart/data": "^1.1.2", "@emoji-mart/react": "^1.1.1", "@emotion/cache": "^11.14.0", "@emotion/react": "^11.14.0", "@emotion/server": "^11.11.0", "@emotion/styled": "^11.14.0", "@giphy/js-fetch-api": "^5.2.0", "@giphy/react-components": "^6.9.4", "@hello-pangea/dnd": "^17.0.0", "@hookform/error-message": "^2.0.1", "@hookform/resolvers": "^3.3.4", "@knocklabs/react": "^0.3.0-rc-4.0", "@lottiefiles/dotlottie-react": "^0.10.1", "@memberup/database": "*", "@memberup/shared": "*", "@mui/icons-material": "^5.0.0 <6.0.0", "@mui/lab": "5.0.0-alpha.105", "@mui/material": "^5.0.0 <6.0.0", "@mui/material-nextjs": "^6.0.1", "@mui/styles": "^5.0.0 <6.0.0", "@mui/system": "^5.0.0 <6.0.0", "@mui/x-date-pickers": "^5.0.0 <6.0.0", "@mux/mux-node": "^7.3.3", "@mux/mux-player-react": "^3.3.0", "@mux/upchunk": "^3.2.0", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.1", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@radix-ui/react-visually-hidden": "^1.1.0", "@react-google-maps/api": "^2.19.3", "@reduxjs/toolkit": "1.9.7", "@sentry/nextjs": "^9.14.0", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@tiptap/core": "^2.11.5", "@tiptap/extension-bubble-menu": "^2.11.5", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-link": "^2.11.5", "@tiptap/extension-underline": "^2.11.5", "@tiptap/react": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@types/chroma-js": "^2.4.4", "@types/draft-js": "^0.11.18", "@types/jsdom": "^21.1.7", "@types/react": "18.3.11", "@types/react-color": "^3.0.12", "@types/react-dom": "18.3.1", "@vercel/speed-insights": "^1.0.12", "axios": "^1.6.4", "babel-plugin-styled-components": "^2.1.4", "cheerio": "1.0.0-rc.12", "chroma-js": "^3.1.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "color": "^4.2.3", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "dompurify": "^3.2.4", "draft-js": "0.11.7", "draft-js-export-html": "^1.4.1", "draft-js-import-html": "^1.4.1", "embla-carousel-fade": "^8.5.2", "embla-carousel-react": "^8.1.3", "emoji-mart": "^5.5.2", "emoji-picker-react": "^4.5.15", "emotion": "^11.0.0", "file-saver": "^2.0.5", "framer-motion": "^10.16.4", "he": "^1.2.0", "html-react-parser": "^5.0.6", "ics": "^3.5.0", "inngest": "3.29.2", "input-otp": "^1.4.1", "joi": "^17.11.0", "joi-password": "^4.1.1", "js-cookie": "^3.0.5", "jsdom": "^26.0.0", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "linkify-react": "^4.1.3", "linkifyjs": "^4.1.3", "lodash": "^4.17.21", "memoize-one": "^6.0.0", "minisearch": "^7.1.1", "moment-timezone": "^0.5.43", "next": "15.3.0", "next-auth": "^5.0.0-beta.25", "next-connect": "0.13.0", "next-redux-wrapper": "^8.1.0", "next-themes": "^0.3.0", "pdfjs-dist": "^5.3.31", "polished": "^4.2.2", "posthog-js": "^1.234.8", "posthog-node": "^4.11.1", "prop-types": "15.8.1", "quill-auto-detect-url": "^0.2.1", "randomstring": "^1.3.0", "react": "18.3.1", "react-color": "^2.19.3", "react-colorful": "^5.6.1", "react-countdown": "^2.3.5", "react-dom": "18.3.1", "react-dropzone": "^14.3.5", "react-easy-crop": "^5.0.2", "react-google-calendar-api": "^2.3.0", "react-h5-audio-player": "^3.9.1", "react-hook-form": "^7.51.4", "react-infinite-scroll-component": "^6.1.0", "react-instantsearch": "^7.3.0", "react-intersection-observer": "^9.15.1", "react-mentions": "^4.4.10", "react-payment-logos": "^1.1.0", "react-phone-number-input": "3.3.7", "react-quill": "^2.0.0", "react-quill-new": "^3.3.3", "react-redux": "8.1.3", "react-responsive": "^10.0.0", "react-select": "^5.8.0", "react-share": "^4.4.1", "react-slick": "^0.29.0", "react-timezone-select": "^2.1.2", "react-toastify": "9.1.3", "react-use": "^17.4.0", "recharts": "2.9.3", "redux-saga": "^1.2.3", "saga-toolkit": "^2.1.2", "sass": "1.69.5", "slick-carousel": "^1.8.1", "sonner": "^1.5.0", "storybook": "^8.3.6", "stream-chat": "^8.40.9", "stream-chat-react": "^12.2.2", "stripe": "^14.15.0", "swr": "^2.2.4", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "unfurl.js": "^6.3.2", "unsplash-js": "^7.0.18", "use-file-picker": "2.1.1", "uuid": "^9.0.1", "uuidv7": "^1.0.2", "vaul": "^0.9.1", "zod": "^3.22.4", "zustand": "^4.5.5", "zustand-computed-state": "^0.1.8"}, "devDependencies": {"@next/bundle-analyzer": "15.3.0", "@storybook/addon-essentials": "^8.3.6", "@storybook/addon-interactions": "^8.3.6", "@storybook/addon-links": "^8.3.6", "@storybook/addon-onboarding": "^8.3.6", "@storybook/addon-themes": "^8.3.6", "@storybook/blocks": "^8.3.6", "@storybook/nextjs": "^8.3.6", "@storybook/react": "^8.3.6", "@storybook/test": "^8.3.6", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/node": "20.6.3", "@types/randomstring": "^1.1.11", "@types/react": "18.3.11", "@types/react-dom": "18.3.1", "@types/react-slick": "^0.23.12", "autoprefixer": "^10.4.17", "babel-plugin-istanbul": "6.1.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "config": "*", "cross-env": "^7.0.3", "jest": "^30.0.3", "node-fetch": "3.3.2", "node-mocks-http": "^1.17.2", "prettier": "^3.0.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "ts-jest": "^29.4.0", "ts-node": "^10.9.2", "typescript": "5.2.2"}, "browser": {"child_process": false, "crypto": false, "stream": false, "util": false}, "nyc": {"all": true, "cwd": "../..", "include": ["apps/memberup/**/*.{js,jsx,ts,tsx}", "packages/shared/src/**/*.{js,jsx,ts,tsx}"], "report-dir": "../../coverage"}, "resolutions": {"@types/react": "18.3.11", "@types/react-dom": "18.3.1"}}