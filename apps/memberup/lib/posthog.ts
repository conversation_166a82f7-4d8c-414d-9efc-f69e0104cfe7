export enum CommunityEvents {
  CREATED = 'community_created',
  CANCELED = 'community_canceled',
  FREE_TRIAL_CANCELED = 'community_free_trial_canceled',
  PAYMENT_RECEIVED = 'community_payment_received',
  SETTINGS_UPDATED = 'community_settings_updated',
}

export enum MemberEvents {
  JOINED_COMMUNIYT = 'member_joined_community',
  LEFT_COMMUNITY = 'member_left_community',
  PAYMENT_RECEIVED = 'member_payment_received',
  REQUESTED_MEMBERSHIP = 'community_joined',
}

export enum UserEvents {
  SIGNED_UP = 'user_signed_up',
  LOGGED_IN = 'user_logged_in',
  LOGGED_OUT = 'user_logged_out',
  PROFILE_UPDATED = 'user_profile_updated',
  ONBOARDING_BIO_COMPLETED = 'user_onboarding_bio_completed',
  ONBOARDING_SELECTED_THEME = 'user_onboarding_theme_selected',
  PASSWORD_RESET = 'user_password_reset',
  PASSWORD_RESET_REQUESTED = 'user_password_reset_requested',
  EMAIL_VERIFICATION_CONFIRMED = 'user_email_verification_confirmed',
  SETTINGS_UPDATED = 'user_theme_updated',
}

export enum FinancialEvents {
  REVENUE_RECOGNIZED = 'revenue_recognized',
  REFUND_PROCESSED = 'refund_processed',
  PAYMENT_FAILED = 'payment_failed',
}

export enum FeedEvents {
  POST_CREATED = 'feed_post_created',
  POST_LIKED = 'feed_post_liked',
  POST_UNLIKED = 'feed_post_unliked',
  COMMENT_ADDED = 'feed_comment_added',
  COMMENT_REMOVED = 'feed_comment_removed',
  COMMENT_LIKED = 'feed_comment_liked',
  COMMENT_UNLIKED = 'feed_comment_unliked',
  POST_REPORTED = 'post_reported',
  POST_MODERATED = 'post_moderated',
}
