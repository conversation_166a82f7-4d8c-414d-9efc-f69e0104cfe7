import chroma from 'chroma-js'
import { ColorResult } from 'react-color'

import { saturationAreaHeight, saturationAreaWidth } from './constants'
import { TColorPickerContrastReferenceColor } from '@/shared-types/types'

export function getHSLString(rgbColor: string) {
  const hsl = chroma(rgbColor).hsl()

  return `${Math.round(hsl[0])} ${Math.round(hsl[1] * 100)}% ${Math.round(hsl[2] * 100)}%`
}

export const hexToRgb = (hex: string): { r: number; g: number; b: number } => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  if (!result) {
    throw new Error('Invalid hex color')
  }
  return {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16),
  }
}

export const calculateLuminance = (hex: string): number => {
  const { r, g, b } = hexToRgb(hex)

  // Convert RGB to sRGB
  const [rs, gs, bs] = [r, g, b].map((c) => {
    const s = c / 255
    return s <= 0.03928 ? s / 12.92 : Math.pow((s + 0.055) / 1.055, 2.4)
  })

  return 0.2126 * rs + 0.7152 * gs + 0.0722 * bs
}

export const calculateContrastRatio = (color1: string, color2: string): number => {
  const l1 = calculateLuminance(color1)
  const l2 = calculateLuminance(color2)

  const lighter = Math.max(l1, l2)
  const darker = Math.min(l1, l2)

  return (lighter + 0.05) / (darker + 0.05)
}

export const meetsContrastRequirements = (
  color: string,
  referenceColors: TColorPickerContrastReferenceColor[],
): boolean => {
  if (referenceColors.length === 0) return true

  return referenceColors.every(
    ({ color: referenceColor, minColorContrastRatio }) =>
      calculateContrastRatio(color, referenceColor) >= minColorContrastRatio,
  )
}

export const hsvToHex = (h: number, s: number, v: number): string => {
  const f = (n: number, k = (n + h / 60) % 6) => v - v * s * Math.max(Math.min(k, 4 - k, 1), 0)
  const r = Math.round(f(5) * 255)
  const g = Math.round(f(3) * 255)
  const b = Math.round(f(1) * 255)
  return (
    '#' +
    [r, g, b]
      .map((x) => x.toString(16).padStart(2, '0'))
      .join('')
      .toUpperCase()
  )
}

export const getDefaultColorFormats = (hex: string) => {
  const c = chroma(hex)
  return {
    hex,
    hsl: { h: c.get('hsl.h'), s: c.get('hsl.s'), l: c.get('hsl.l') },
    hsv: { h: c.get('hsv.h'), s: c.get('hsv.s'), v: c.get('hsv.v') },
    rgb: { r: c.get('rgb.r'), g: c.get('rgb.g'), b: c.get('rgb.b') },
  }
}

// Find the saturation boundary y for a given x
export const findSaturationBoundaryY = (
  x: number,
  isUpper: boolean,
  height: number,
  width: number,
  hue: number,
  referenceColors: TColorPickerContrastReferenceColor[],
): number => {
  let low = 0
  let high = height - 1
  let result = isUpper ? height : 0

  while (low <= high) {
    const mid = Math.floor((low + high) / 2)
    const s = x / (width - 1)
    const v = 1 - mid / (height - 1)
    const color = hsvToHex(hue, s, v)
    const isValid = meetsContrastRequirements(color, referenceColors)

    if (isUpper) {
      if (isValid) {
        result = mid
        high = mid - 1
      } else {
        low = mid + 1
      }
    } else {
      if (isValid) {
        result = mid
        low = mid + 1
      } else {
        high = mid - 1
      }
    }
  }

  return result
}

export const getColorContrastCurves = (
  width: number,
  height: number,
  hue: number,
  referenceColors: TColorPickerContrastReferenceColor[],
) => {
  const upperCurve = []
  const lowerCurve = []
  const isBlueHue = hue >= 196 && hue <= 282
  const blueCutoff = Math.floor(width * 0.6)
  let blueAnchorY = null

  for (let x = 0; x < width; x++) {
    if (isBlueHue && x === blueCutoff) {
      blueAnchorY = findSaturationBoundaryY(x, true, height, width, hue, referenceColors)
      upperCurve[x] = blueAnchorY
    } else if (isBlueHue && x > blueCutoff) {
      // Smoothly interpolate from blueAnchorY to 0 as x goes from blueCutoff to width-1
      const t = (x - blueCutoff) / (width - 1 - blueCutoff)
      upperCurve[x] = Math.round(blueAnchorY * (1 - t))
    } else {
      upperCurve[x] = findSaturationBoundaryY(x, true, height, width, hue, referenceColors)
    }
    lowerCurve[x] = findSaturationBoundaryY(x, false, height, width, hue, referenceColors)
  }
  return { upperCurve, lowerCurve }
}

export const isColorPositionValid = (upperCurve: number[], lowerCurve: number[], s: number, v: number) => {
  if (!upperCurve || !lowerCurve) return true

  const x = Math.max(0, Math.min(Math.round(s * (saturationAreaWidth - 1)), saturationAreaWidth - 1))
  const y = Math.max(0, Math.min(Math.round((1 - v) * (saturationAreaHeight - 1)), saturationAreaHeight - 1))

  return y >= upperCurve[x] && y <= lowerCurve[x]
}

export const getCenterColor = (hue: number): ColorResult => {
  // HSV: h = current, s = 0.5, v = 0.5
  const s = 0.5
  const v = 0.5
  // Convert HSV to RGB
  const c = v * s
  const x = c * (1 - Math.abs(((hue / 60) % 2) - 1))
  const m = v - c
  let r = 0,
    g = 0,
    b = 0
  if (hue < 60) [r, g, b] = [c, x, 0]
  else if (hue < 120) [r, g, b] = [x, c, 0]
  else if (hue < 180) [r, g, b] = [0, c, x]
  else if (hue < 240) [r, g, b] = [0, x, c]
  else if (hue < 300) [r, g, b] = [x, 0, c]
  else [r, g, b] = [c, 0, x]
  r = Math.round((r + m) * 255)
  g = Math.round((g + m) * 255)
  b = Math.round((b + m) * 255)
  const hex = `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
  return {
    hex,
    rgb: { r, g, b, a: 1 },
    hsl: { h: hue, s: 0.5, l: 0.5, a: 1 },
  }
}

export const getLastValidColor = (
  hue: number,
  upperCurve: number[],
  lowerCurve: number[],
  width: number,
  height: number,
): ColorResult => {
  // Find the last valid x position
  let lastValidX = -1
  let lastValidY = -1

  // Search from right to left to find the last valid position
  for (let x = width - 1; x >= 0; x--) {
    const upperY = upperCurve[x]
    const lowerY = lowerCurve[x]

    // If there's a valid range between upper and lower curves
    if (upperY < lowerY) {
      lastValidX = x
      // Use the middle point between upper and lower curves
      lastValidY = Math.floor((upperY + lowerY) / 2)
      break
    }
  }

  // If no valid position found, default to center
  if (lastValidX === -1) {
    return getCenterColor(hue)
  }

  // Convert position back to HSV
  const s = lastValidX / (width - 1)
  const v = 1 - lastValidY / (height - 1)

  // Convert HSV to RGB
  const c = v * s
  const x = c * (1 - Math.abs(((hue / 60) % 2) - 1))
  const m = v - c
  let r = 0,
    g = 0,
    b = 0

  if (hue < 60) [r, g, b] = [c, x, 0]
  else if (hue < 120) [r, g, b] = [x, c, 0]
  else if (hue < 180) [r, g, b] = [0, c, x]
  else if (hue < 240) [r, g, b] = [0, x, c]
  else if (hue < 300) [r, g, b] = [x, 0, c]
  else [r, g, b] = [c, 0, x]

  r = Math.round((r + m) * 255)
  g = Math.round((g + m) * 255)
  b = Math.round((b + m) * 255)

  const hex = `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`

  return {
    hex,
    rgb: { r, g, b, a: 1 },
    hsl: { h: hue, s, l: v, a: 1 },
  }
}

export const getNearestValidColorAtX = (
  hue: number,
  s: number,
  v: number,
  upperCurve: number[],
  lowerCurve: number[],
  width: number,
  height: number,
): ColorResult => {
  // Clamp x to valid range
  const x = Math.max(0, Math.min(Math.round(s * (width - 1)), width - 1))
  const upperY = upperCurve[x]
  const lowerY = lowerCurve[x]
  if (upperY < lowerY) {
    // Find the y (value) closest to the original v
    const yFromV = Math.round((1 - v) * (height - 1))
    let bestY = upperY
    let minDist = Math.abs(yFromV - upperY)
    for (let y = upperY; y <= lowerY; y++) {
      const dist = Math.abs(yFromV - y)
      if (dist < minDist) {
        minDist = dist
        bestY = y
      }
    }
    const newV = 1 - bestY / (height - 1)
    // Convert HSV to RGB
    const c = newV * s
    const xComp = c * (1 - Math.abs(((hue / 60) % 2) - 1))
    const m = newV - c
    let r = 0,
      g = 0,
      b = 0
    if (hue < 60) [r, g, b] = [c, xComp, 0]
    else if (hue < 120) [r, g, b] = [xComp, c, 0]
    else if (hue < 180) [r, g, b] = [0, c, xComp]
    else if (hue < 240) [r, g, b] = [0, xComp, c]
    else if (hue < 300) [r, g, b] = [xComp, 0, c]
    else [r, g, b] = [c, 0, xComp]
    r = Math.round((r + m) * 255)
    g = Math.round((g + m) * 255)
    b = Math.round((b + m) * 255)
    const hex = `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
    return {
      hex,
      rgb: { r, g, b, a: 1 },
      hsl: { h: hue, s, l: newV, a: 1 },
    }
  }
  // Fallback to last valid color in the area
  return getLastValidColor(hue, upperCurve, lowerCurve, width, height)
}

export const isValidHexColorFormat = (value: string) => /^#[0-9A-F]{0,6}$/i.test(value)

export const getContrastCheckForHexInputColor = (value: string, upperCurve: number[], lowerCurve: number[]) => {
  const color = chroma(value)
  const hsv = color.hsv()
  return isColorPositionValid(
    upperCurve,
    lowerCurve,
    hsv[1], // saturation
    hsv[2], // value
  )
}
