import { redirect } from 'next/navigation'

import { getRedirectPath, isProtectedRoute } from '../route-protection.utils'
import { auth } from '@/auth'

export async function checkRouteProtection(pathname: string) {
  if (!isProtectedRoute(pathname)) {
    return null
  }

  const session = await auth()

  if (!session) {
    const redirectPath = getRedirectPath(pathname)
    if (redirectPath) {
      redirect(redirectPath)
    }
  }

  return session
}
