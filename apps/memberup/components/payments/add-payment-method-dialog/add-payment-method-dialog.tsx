'use client'

import * as VisuallyHidden from '@radix-ui/react-visually-hidden'
import { Elements } from '@stripe/react-stripe-js'
import { loadStripe, PaymentMethod } from '@stripe/stripe-js'
import { useEffect, useState } from 'react'

import { PaymentMethodForm } from './payment-method-form'
import { useMounted } from '@memberup/shared/src/components/hooks/use-mounted'
import { STRIPE_PUBLISH_KEY } from '@memberup/shared/src/config/envs'
import {
  createStripeSetupIntentApi,
  setStripeDefaultPaymentMethodApi,
} from '@memberup/shared/src/services/apis/stripe.api'
import { THEME_MODE_ENUM } from '@memberup/shared/src/types/enum'
import { ErrorMessageRetry } from '@/components/layout/error-message-retry/error-message-retry'
import { SkeletonBox } from '@/components/ui'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  <PERSON><PERSON><PERSON>nner,
  DialogTitle,
} from '@/components/ui/dialog'
import { toast } from '@/components/ui/sonner'
import { unexpectedError } from '@/lib/error-messages'
import { selectMembershipSetting } from '@/memberup/store/features/membershipSlice'
import { mergeUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

export function AddPaymentMethodDialog({
  onAddPaymentMethod,
  open,
  setOpen,
  paymentMethods,
}: {
  onAddPaymentMethod: (paymentMethod: PaymentMethod) => void
  open: boolean
  setOpen: (value: boolean) => void
  paymentMethods: PaymentMethod[]
}) {
  const dispatch = useAppDispatch()
  const membershipSetting = useAppSelector(selectMembershipSetting)
  const [clientSecret, setClientSecret] = useState<string | null>(null)
  const [stripeLoading, setStripeLoading] = useState(true)
  const [hasError, setHasError] = useState(false)
  const [updateDefault, setUpdateDefault] = useState(true)
  const mountedRef = useMounted(true)

  const stripePromise = loadStripe(
    membershipSetting?.stripe_connect_account?.stripe_publishable_key || STRIPE_PUBLISH_KEY,
  )

  const showCardElement = !stripeLoading && !hasError && clientSecret && stripePromise

  const fetchPaymentIntent = async () => {
    try {
      setStripeLoading(true)
      setHasError(false)
      const response = await createStripeSetupIntentApi(false, {
        mode: 'setup',
        payment_method_types: ['card'],
      })
      if (mountedRef.current) {
        setClientSecret(response.data.data?.['client_secret'])
        setStripeLoading(false)
      }
    } catch (err) {
      console.error(err.response?.message)
      if (mountedRef.current) {
        setStripeLoading(false)
        setHasError(true)
      }
    }
  }

  useEffect(() => {
    if (open) {
      setClientSecret(null)
      fetchPaymentIntent()
    }
  }, [open])

  const onSuccess = async (paymentMethod: PaymentMethod) => {
    if (paymentMethod) {
      try {
        if (updateDefault || !paymentMethods || paymentMethods.length === 0) {
          await setStripeDefaultPaymentMethodApi(false, paymentMethod.id)
          dispatch(mergeUserProfile({ stripe_payment_method_id: paymentMethod.id }))
        }

        onAddPaymentMethod(paymentMethod)
        setOpen(false)
        toast.success('Payment method added successfully.')
      } catch (err) {
        toast.error(err.response?.message || unexpectedError)
      }
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Payment Method</DialogTitle>
          <VisuallyHidden.Root>
            <DialogDescription>Add a payment method to your account</DialogDescription>
          </VisuallyHidden.Root>
        </DialogHeader>

        {stripeLoading && (
          <DialogInner>
            <SkeletonBox />
          </DialogInner>
        )}

        {hasError && (
          <DialogInner>
            <ErrorMessageRetry
              onRetry={fetchPaymentIntent}
              errorDescription="An error occurred while setting up the payment method."
            />
          </DialogInner>
        )}

        {showCardElement && (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret: clientSecret,
              appearance: {
                theme: membershipSetting?.theme_mode === THEME_MODE_ENUM.dark ? 'night' : 'stripe',
              },
              loader: 'always',
            }}
          >
            <PaymentMethodForm
              paymentMethods={paymentMethods}
              onSuccess={onSuccess}
              onCancel={() => setOpen(false)}
              updateDefault={updateDefault}
              setUpdateDefault={setUpdateDefault}
              clientSecret={clientSecret}
            />
          </Elements>
        )}
      </DialogContent>
    </Dialog>
  )
}
