'use client'

import { useEffect, useRef } from 'react'
import { StreamChat } from 'stream-chat'
import { Chat } from 'stream-chat-react'

import { GET_STREAM_APP_KEY } from '@memberup/shared/src/config/envs'
import { useStore } from '@/hooks/useStore'
import { getMembers } from '@/memberup/store/features/memberSlice'
import { useAppDispatch } from '@/memberup/store/store'
import { USER_STATUS_ENUM, VISIBILITY_ENUM } from '@/shared-types/enum'

export function StreamChatProvider({ children }: { children: React.ReactNode }): React.ReactNode {
  const dispatch = useAppDispatch()

  const user = useStore((state) => state.auth.user)
  const streamChatUserToken = useStore((state) => state.auth.streamChatUserToken)
  const setConnected = useStore((state) => state.streamChat.setConnected)
  const setConnecting = useStore((state) => state.streamChat.setConnecting)

  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useStore((state) => state.community.membership?.membership_setting)
  const userMembership = useStore((state) =>
    membership ? state.auth.user?.user_memberships.find((um) => um.membership.id === membership.id) : null,
  )
  const gettingForMembershipRef = useRef(null)
  const gettingForUserRef = useRef(null)

  const getStreamChatClient = () => {
    let client = null

    try {
      client = StreamChat.getInstance(GET_STREAM_APP_KEY, {
        enableInsights: true,
        enableWSFallback: true,
        timeout: 10000,
      })

      return client
    } catch (error) {
      console.error('Error connecting anonymous user:', error)
    }

    return client
  }

  // TODO: remove useRef
  const chatClientRef = useRef<StreamChat | null>(getStreamChatClient())

  // Immediately set connected to false when user state changes require reconnection
  useEffect(() => {
    if (!chatClientRef.current) return

    const currentStreamUserId = chatClientRef.current.user?.id
    const isAnonymous = currentStreamUserId?.startsWith('!anon') || chatClientRef.current.user?.anon === true

    // Set connected to false and connecting to true immediately if:
    // 1. User logged out but stream client still has non-anonymous user
    // 2. User logged in but stream client doesn't have the correct user
    if ((!user && currentStreamUserId && !isAnonymous) || (user && currentStreamUserId !== user.id)) {
      setConnected(false)
      setConnecting(true)
    }
  }, [user, setConnected, setConnecting])

  useEffect(() => {
    const connectAnonymousUser = async () => {
      await chatClientRef.current.connectAnonymousUser()
      setConnected(true)
      setConnecting(false)
    }

    const connectAuthenticatedUser = async () => {
      await chatClientRef.current.connectUser({ id: user.id }, streamChatUserToken)
      setConnected(true)
      setConnecting(false)
    }

    const connectOrReconnect = async () => {
      if (!chatClientRef.current) return

      let connect = false
      const currentStreamUserId = chatClientRef.current.user?.id
      const isAnonymous = currentStreamUserId?.startsWith('!anon') || chatClientRef.current.user?.anon === true

      if (!user && currentStreamUserId && !isAnonymous) {
        // User logged out but stream client still has authenticated (non-anonymous) user
        connect = true
        setConnected(false)
        setConnecting(true)

        await chatClientRef.current.disconnectUser()
      } else if (user && streamChatUserToken && (!currentStreamUserId || currentStreamUserId !== user.id)) {
        // User logged in but stream client doesn't have the correct user
        connect = true
        setConnected(false)
        setConnecting(true)

        if (currentStreamUserId) {
          await chatClientRef.current.disconnectUser()
        }
      } else if (!user && !currentStreamUserId) {
        // No user and no stream user - need to connect anonymously
        connect = true
        setConnected(false)
        setConnecting(true)
      }

      if (connect) {
        if (!user) {
          connectAnonymousUser()
        } else {
          connectAuthenticatedUser()
        }
      }
    }

    connectOrReconnect()
  }, [setConnecting, setConnected, streamChatUserToken, user])

  useEffect(() => {
    if (!membership) {
      return
    }

    if (
      (!gettingForMembershipRef.current ||
        (membership && gettingForMembershipRef.current !== membership.id) ||
        gettingForUserRef.current !== user?.id) &&
      (membershipSetting?.visibility === VISIBILITY_ENUM.public ||
        (userMembership && userMembership.status === 'accepted'))
    ) {
      dispatch(
        getMembers({
          membershipId: membership.id,
          where: JSON.stringify({
            status: USER_STATUS_ENUM.active,
          }),
          take: 10000,
          skip: 0,
        }),
      )
      gettingForMembershipRef.current = membership.id
      gettingForUserRef.current = user?.id
    } else if ((gettingForMembershipRef.current && !membership) || (!user && gettingForUserRef.current)) {
      gettingForMembershipRef.current = null
      gettingForUserRef.current = null
    }
  }, [user, membership])
  /*

  useEffect(() => {
    // NOTE: We need to fetch the initial state for the inbox channels when the StreamChat client is ready (authenticated)
    if (!streamChatClient || !connected || !user) {
      return
    }

    const fetchInboxChannels = async () => {
      const filters = {
        members: { $in: [streamChatClient.userID] },
        type: CHANNEL_TYPE_ENUM.messaging,
      }
      const channels = await streamChatClient.queryChannels(filters)
      dispatch(setInboxChannels(channels))
    }

    const handleNewInboxMessage = async (event: any) => {
      if (event.channel_type !== 'messaging') return
      await fetchInboxChannels()
    }

    fetchInboxChannels()

    streamChatClient.on('message.new', handleNewInboxMessage)

    return () => {
      if (!streamChatClient) return
      streamChatClient.off('message.new', handleNewInboxMessage)
    }
  }, [streamChatClient, connected, dispatch, user])
  */

  return (
    <div>
      <Chat
        client={chatClientRef.current}
        theme="messaging"
        customClasses={{
          chat: 'custom-chat-class',
          channel: 'custom-channel-class',
        }}
      >
        {children}
      </Chat>
    </div>
  )
}
