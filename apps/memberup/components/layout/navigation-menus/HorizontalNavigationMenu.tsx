import React, { useEffect, useRef, useState } from 'react'

import { Carousel, CarouselContent } from '@/components/ui/carousel'
import { cn } from '@/lib/utils'

export function HorizontalNavigationMenu({
  children,
  className,
  selectedIndex,
}: {
  children: React.ReactNode
  className?: string
  selectedIndex?: number
}) {
  const carouselApiRef = useRef<{ scrollTo: (index: number) => void } | null>(null)
  const [isApiReady, setIsApiReady] = useState(false)

  useEffect(() => {
    if (carouselApiRef.current && typeof selectedIndex === 'number' && selectedIndex >= 0 && isApiReady) {
      carouselApiRef.current.scrollTo(selectedIndex)
    }
  }, [selectedIndex, isApiReady])

  return (
    <div className={cn('flex w-full justify-center', className)}>
      <nav className="content-container pb-3">
        <Carousel
          setApi={(api) => {
            carouselApiRef.current = api
            setIsApiReady(true)
          }}
        >
          <CarouselContent className="-ml-1.5 pl-4">{children}</CarouselContent>
        </Carousel>
      </nav>
    </div>
  )
}
