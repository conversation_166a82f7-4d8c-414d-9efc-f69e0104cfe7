import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { useParams, usePathname } from 'next/navigation'
import { useState } from 'react'

import { HorizontalNavigationMenu } from './HorizontalNavigationMenu'
import { HorizontalNavigationMenuItem } from './HorizontalNavigationMenuItem'
import { AdminSettings } from '@/components/settings/admin-settings/AdminSettings'
import { MemberSettings } from '@/components/settings/member-settings'
import { useStore } from '@/hooks/useStore'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'

export function CommunityHorizontalNavigationMenu() {
  const params = useParams()
  const pathname = usePathname()

  const menuItems = [
    { label: 'Community', href: `/${params.slug}` },
    { label: 'Content', href: `/${params.slug}/content` },
    { label: 'Events', href: `/${params.slug}/events` },
    { label: 'Members', href: `/${params.slug}/members` },
    { label: 'About', href: `/${params.slug}/about` },
  ]

  const selectedIndex = menuItems.findIndex((item) => pathname === item.href)

  const [settingsModalOpen, setSettingsModalOpen] = useState(false)
  const user = useStore((state) => state.auth.user)
  const { isCurrentUserAdmin } = useCheckUserRole()

  return (
    <>
      <HorizontalNavigationMenu selectedIndex={selectedIndex}>
        {menuItems.map((item) => (
          <HorizontalNavigationMenuItem key={item.href} href={item.href}>
            {item.label}
          </HorizontalNavigationMenuItem>
        ))}
        {user && (
          <HorizontalNavigationMenuItem onClick={() => setSettingsModalOpen(true)}>
            Settings
          </HorizontalNavigationMenuItem>
        )}
      </HorizontalNavigationMenu>
      <VisuallyHidden>
        {isCurrentUserAdmin ? (
          <AdminSettings open={settingsModalOpen} onOpenChange={() => setSettingsModalOpen(false)} />
        ) : (
          <MemberSettings open={settingsModalOpen} onOpenChange={() => setSettingsModalOpen(false)} />
        )}
      </VisuallyHidden>
    </>
  )
}
