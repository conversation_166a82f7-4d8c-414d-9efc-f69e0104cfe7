import { Refresh20Icon } from '@/components/icons/20px/refresh-20-icon'
import { Button } from '@/components/ui/button'
import { unexpectedError } from '@/lib/error-messages'

interface ErrorMessageRetryProps {
  onRetry: () => void
  errorDescription?: string
}

export function ErrorMessageRetry({ onRetry, errorDescription = unexpectedError }: ErrorMessageRetryProps) {
  return (
    <div className="flex flex-col items-center justify-center text-center">
      <p className="mb-6 text-base">{errorDescription} Please check your internet connection and try again.</p>
      <p className="text-base">
        If the problem persists, please contact support at{' '}
        <a href="mailto:<EMAIL>" className="text-community-primary">
          <EMAIL>
        </a>
      </p>
      {onRetry && (
        <Button className="mt-12" onClick={onRetry} variant="community-primary">
          <Refresh20Icon className="mr-2 h-5 w-5" /> Retry
        </Button>
      )}
    </div>
  )
}
