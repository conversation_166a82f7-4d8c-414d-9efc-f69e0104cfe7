import type { Meta, StoryObj } from '@storybook/react'

import { ErrorMessageRetry } from './error-message-retry'

const meta: Meta<typeof ErrorMessageRetry> = {
  title: 'Layout/ErrorMessageRetry',
  component: ErrorMessageRetry,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onRetry: {
      description: 'Callback function to be called when the retry button is clicked',
      control: false,
    },
    errorDescription: {
      description:
        'Custom error description to display. If not provided, will use the default unexpected error message',
      control: 'text',
    },
  },
}

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {
    onRetry: () => console.log('Retry clicked'),
  },
}

export const WithCustomError: Story = {
  args: {
    onRetry: () => console.log('Retry clicked'),
    errorDescription: 'An error occurred while setting up the payment method.',
  },
}

export const WithLongCustomError: Story = {
  args: {
    onRetry: () => console.log('Retry clicked'),
    errorDescription: 'An error occurred while processing your request. The server is not responding.',
  },
}
