import { TopNavbar } from '../top-navbar'

export function FixedHeightLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <TopNavbar />
      <div className="absolute left-0 top-[7.6875rem] flex h-[calc(100%-7.6875rem)] w-full justify-center md:top-[8.6875rem] md:h-[calc(100%-8.6875rem)] xl:top-[4.9375rem] xl:h-[calc(100%-4.9375rem)]">
        <div className="content-container h-full px-4">{children}</div>
      </div>
    </>
  )
}
