'use client'

import { VisuallyHidden } from '@radix-ui/react-visually-hidden'
import { AnimatePresence, motion } from 'framer-motion'
import { ReactNode, useState } from 'react'

import { LoginForm } from '@/components/auth/login-form'
import { PasswordResetForm } from '@/components/auth/password-rest-form/password-reset-form'
import { SignUpForm } from '@/components/auth/signup-form'
import { VerifyEmailForm } from '@/components/auth/verify-email-form'
import { JoinCommunity } from '@/components/community/join-community'
import { ParticlesContainer } from '@/components/layout/particles-container'
import { CustomizeYourCommunityForm } from '@/components/settings/customize-your-community-form/customize-your-community-form'
import { CustomizeYourThemeForm } from '@/components/settings/customize-your-theme-form'
import { ProfileSetupForm } from '@/components/settings/profile-setup-form'
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogInner, DialogTitle } from '@/components/ui/dialog'
import { useStore } from '@/hooks/useStore'
import { AuthForms } from '@/store/authSlice'

const formVariants = {
  initial: {
    opacity: 0,
  },
  animate: {
    opacity: 1,
    transition: {
      duration: 0.6,
      ease: 'easeOut',
    },
  },
  exit: {
    opacity: 0,
    transition: {
      duration: 0.2,
      ease: 'linear',
    },
  },
}

interface AnimatedFormWrapperProps {
  children: ReactNode
}

const AnimatedFormWrapper = ({ children }: AnimatedFormWrapperProps) => (
  <motion.div variants={formVariants} initial="initial" animate="animate" exit="exit" className="w-full">
    {children}
  </motion.div>
)

export function AuthModal() {
  const activeModalForm = useStore((state) => state.auth.activeModalForm)
  const setShowForm = useStore((state) => state.auth.setShowForm)
  const setJoinCommunityId = useStore((state) => state.auth.setJoinCommunityId)
  const [passwordReset, setPasswordReset] = useState(false)

  return (
    <Dialog
      open={activeModalForm !== null}
      onOpenChange={() => {
        setShowForm(null)
        setJoinCommunityId(null)
      }}
    >
      <DialogContent aria-describedby={undefined} className="overflow-hidden">
        <DialogHeader>
          <VisuallyHidden>
            <DialogTitle>Join Community</DialogTitle>
          </VisuallyHidden>
        </DialogHeader>
        <DialogInner>
          <AnimatePresence mode="wait">
            {activeModalForm === AuthForms.login && (
              <AnimatedFormWrapper key="login">
                {passwordReset ? (
                  <PasswordResetForm backToLogin={() => setPasswordReset(false)} />
                ) : (
                  <LoginForm
                    mode="modal"
                    onSuccess={() => setShowForm(null)}
                    onForgotPasswordClick={() => setPasswordReset(true)}
                  />
                )}
              </AnimatedFormWrapper>
            )}
            {activeModalForm === AuthForms.signup && (
              <AnimatedFormWrapper key="signup">
                <SignUpForm mode="modal" />
              </AnimatedFormWrapper>
            )}
            {activeModalForm === AuthForms.verifyEmail && (
              <AnimatedFormWrapper key="verify-email">
                <VerifyEmailForm />
              </AnimatedFormWrapper>
            )}
            {activeModalForm === AuthForms.joinCommunity && (
              <AnimatedFormWrapper key="join-community">
                <JoinCommunity />
              </AnimatedFormWrapper>
            )}
            {activeModalForm === AuthForms.profileSetup && (
              <AnimatedFormWrapper key="profile-setup">
                <ProfileSetupForm />
              </AnimatedFormWrapper>
            )}
            {activeModalForm === AuthForms.customizeTheme && (
              <AnimatedFormWrapper key="customize-theme">
                <div className="text-white mb-7 text-center text-lg font-semibold">Choose Your Theme</div>
                <CustomizeYourThemeForm buttonText="Next" variant="modal" />
              </AnimatedFormWrapper>
            )}
            {activeModalForm === AuthForms.customizeCommunity && (
              <AnimatedFormWrapper key="customize-community">
                <ParticlesContainer className="absolute left-0 top-0 z-0 h-full w-full" />
                <div className="relative z-10">
                  <div className="text-white mb-7 text-center text-lg font-semibold">Customize your theme</div>
                  <CustomizeYourCommunityForm />
                </div>
              </AnimatedFormWrapper>
            )}
          </AnimatePresence>
        </DialogInner>
      </DialogContent>
    </Dialog>
  )
}
