import { FeedItem } from '@knocklabs/client'
import type { Recipient, User } from '@knocklabs/client/dist/types/interfaces'
import { useKnockFeed } from '@knocklabs/react'
import Link from 'next/link'
import { useState } from 'react'

import { CalendarSelection24Icon } from '@/components/icons'
import { CroppedImage } from '@/components/images/cropped-image'
import { ProfilePicture } from '@/components/images/profile-picture'
import { Button } from '@/components/ui'
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { formatDistanceToNowMinimal } from '@/lib/dates'
import { cn } from '@/lib/utils'
import { KNOCK_WORKFLOW_ENUM } from '@/shared-types/enum'
import { TAppCropArea } from '@/shared-types/types'

export function NotificationsMenuItem({
  faviconData,
  item,
  onItemClick,
}: {
  faviconData?: {
    favicon: string
    faviconCropArea: TAppCropArea
  }
  item: FeedItem
  onItemClick?: () => void
}) {
  const { feedClient } = useKnockFeed()
  const [changingReadStatus, setChangingReadStatus] = useState(false)
  const actor: Recipient | undefined = item?.actors?.[0]
  const data = item?.activities?.[0]?.data
  const eventType = item?.source?.key

  let communitySlug = typeof data?.community_slug === 'string' && data.community_slug.slice(1)
  let commentId = null
  let href = null

  const isNewCommentNotification = eventType === KNOCK_WORKFLOW_ENUM.new_comment_notification
  const isEventNotification = eventType === KNOCK_WORKFLOW_ENUM.event
  const isNewMentionNotification = eventType === KNOCK_WORKFLOW_ENUM.new_mention_notification
  const isNewEveryoneMentionNotification = eventType === KNOCK_WORKFLOW_ENUM.new_everyone_mention_notification
  const isPostReportNotification = eventType === KNOCK_WORKFLOW_ENUM.feed_post_moderation_report_notification

  if (isNewCommentNotification || isNewMentionNotification || isNewEveryoneMentionNotification) {
    // Marking props as optional to avoid errors with existing notifications that may be malformed
    commentId = data?.email_post_url?.split('comment_id=')[1]
    const postSlug = data?.email_post_url?.match(/post\/([^?]+)/)?.[1]
    href = `/${communitySlug}/post/${postSlug}${commentId ? `?comment_id=${commentId}` : ''}`
  } else if (isEventNotification) {
    const eventUrlMatch = data.event_url?.match(/^https?:\/\/([^.]+)/)
    communitySlug = eventUrlMatch?.[1] || communitySlug
    href = `/${communitySlug}/events/${data.id}`
  } else if (isPostReportNotification) {
    href = `/${communitySlug}?settings=moderation`
  }

  const toggleRead = async (event?: React.MouseEvent<HTMLButtonElement>) => {
    event?.stopPropagation()
    event?.preventDefault()

    if (changingReadStatus) return
    setChangingReadStatus(true)

    if (item.read_at) {
      await feedClient.markAsUnread(item)
    } else {
      await feedClient.markAsRead(item)
    }

    setChangingReadStatus(false)
  }

  const onClick = () => {
    onItemClick?.()

    if (!item.read_at) {
      toggleRead()
    }
  }

  function isUser(actor: Recipient): actor is User {
    return actor && typeof actor === 'object' && 'image' in actor && 'name' in actor
  }

  return (
    <Link
      className="group flex w-[16.9375rem] border-b border-b-grey-200 p-2.5 last:border-0 hover:bg-white-100 dark:border-b-grey-900 dark:hover:bg-black-300 md:w-[calc(24.5rem-2px)]"
      href={href ?? ''}
      onClick={onClick}
    >
      {actor && (
        <div className="relative mr-3">
          <ProfilePicture
            className="h-10 w-10 shrink-0 rounded-full"
            src={isUser(actor) ? actor.image : undefined}
            cropArea={
              'image_crop_area' in (actor ?? {})
                ? (actor as { image_crop_area?: TAppCropArea }).image_crop_area
                : undefined
            }
            alt={isUser(actor) ? (actor.name ?? '') : ''}
            height={40}
            width={40}
          />
          {faviconData && (
            <CroppedImage
              className="absolute -bottom-px -right-1 h-4 w-4 overflow-hidden rounded-[0.25rem]"
              src={faviconData.favicon}
              cropArea={faviconData.faviconCropArea}
              alt={communitySlug}
              height={16}
              width={16}
            />
          )}
        </div>
      )}
      {item.source.key === KNOCK_WORKFLOW_ENUM.event && (
        <div className="mr-3 flex h-10 w-10 items-center justify-center rounded-full bg-white-100 group-hover:bg-white-300 dark:bg-black-300 group-hover:dark:bg-black-500">
          <CalendarSelection24Icon className="h-5 w-5 text-black-200 dark:text-black-100" />
        </div>
      )}
      <div className="min-w-0 flex-1 overflow-hidden whitespace-nowrap md:flex md:flex-col md:justify-center">
        <div className="md:flex">
          {actor && !isPostReportNotification && (
            <div className="mb-0.5 mr-1 truncate text-xs font-semibold text-black-700 dark:text-white-500">
              {isUser(actor) ? actor.name : ''}
            </div>
          )}
          <div className="text-xs">
            <span className="text-black-600 dark:text-white-200">
              {isNewEveryoneMentionNotification && 'Mentioned you'}
              {isNewCommentNotification && 'New comment'}
              {isNewMentionNotification && 'Mentioned you'}
              {isEventNotification && 'Events'}
              {isPostReportNotification && 'A post has been reported'}
            </span>
            <span>&nbsp;•&nbsp;{formatDistanceToNowMinimal(new Date(item.inserted_at))}</span>
          </div>
        </div>
        <div className="truncate text-xs text-black-200 dark:text-black-100">
          {(isNewCommentNotification ||
            isNewMentionNotification ||
            isNewEveryoneMentionNotification ||
            isPostReportNotification) &&
            data.message_content.replace('\n', ' ').slice(0, 60)}
          {isEventNotification && item.data.event_title}
        </div>
      </div>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button className="ml-2 shrink-0 p-2" onClick={toggleRead} variant="inline">
              <span className={cn('h-2.5 w-2.5 rounded-full', item.read_at ? 'bg-grey-500' : 'bg-primary-200')} />
            </Button>
          </TooltipTrigger>
          <TooltipContent side="bottom">{item.read_at ? 'Mark as unread' : 'Mark as read'}</TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </Link>
  )
}
