'use client'

import { useCallback, useEffect, useState } from 'react'
import { ColorResult, CustomPicker } from 'react-color'
import { Hu<PERSON>, Saturation } from 'react-color/lib/components/common'

import { getContrastCheckForHexInputColor, isColorPositionValid, isValidHexColorFormat } from '@/lib/colors'

interface ColorPickerProps {
  hex: string
  hsl: { h: number; s: number; l: number; a?: number }
  hsv: { h: number; s: number; v: number; a?: number }
  rgb: { r: number; g: number; b: number; a?: number }
  onChange: (color: ColorResult) => void
  upperCurve?: number[]
  lowerCurve?: number[]
}

export const ColorPicker = CustomPicker(
  ({ hex, hsl, hsv, rgb, onChange, upperCurve, lowerCurve }: ColorPickerProps) => {
    const [lastSelectedColor, setLastSelectedColor] = useState<ColorResult>({ hex, hsl, rgb })
    const [lastValidColor, setLastValidColor] = useState<ColorResult>({ hex, hsl, rgb })
    const [inputValue, setInputValue] = useState(hex)
    const [errorMessage, setErrorMessage] = useState<string | null>(null)

    useEffect(() => {
      setInputValue(hex)
    }, [hex])

    const isValid = useCallback(
      (color: ColorResult) => {
        const hsvColor = (color as any).hsv ?? hsv
        return isColorPositionValid(upperCurve, lowerCurve, hsvColor.s, hsvColor.v)
      },
      [hsv, upperCurve, lowerCurve],
    )

    useEffect(() => {
      const newColor: ColorResult = { hex, hsl, rgb }
      setLastSelectedColor(newColor)
      if (isValid(newColor)) {
        setLastValidColor(newColor)
      }
    }, [hex, hsl, rgb, isValid])

    const isValidColor = isColorPositionValid(upperCurve, lowerCurve, hsv.s, hsv.v)

    const handleMouseUpOrLeave = () => {
      const hsvColor = (lastSelectedColor as any).hsv ?? hsv
      const valid = isColorPositionValid(upperCurve, lowerCurve, hsvColor.s, hsvColor.v)

      if (!valid && lastSelectedColor.hex !== lastValidColor.hex) {
        onChange(lastValidColor)
        setLastSelectedColor(lastValidColor)
      }
    }

    const handleChange = (color: ColorResult) => {
      setLastSelectedColor(color)
      if (isValid(color)) {
        setLastValidColor(color)
        onChange(color)
      }
    }

    return (
      <div className="bg-white w-64 rounded-lg border p-4 shadow-lg dark:border-grey-900 dark:bg-black-700">
        {/* Saturation area */}
        <div className="relative mb-3 h-40 w-full" onMouseUp={handleMouseUpOrLeave} onMouseLeave={handleMouseUpOrLeave}>
          <Saturation
            className="h-full w-full cursor-pointer rounded"
            pointer={() => (
              <div
                className="absolute z-100 h-4 w-4 -translate-x-2 -translate-y-2 rounded-full border-2 shadow-sm"
                style={{
                  borderColor: isValidColor ? '#ffffff' : '#ff0000',
                }}
              />
            )}
            {...{ hsl, hsv, onChange: handleChange }}
          />
        </div>

        {/* Hue slider */}
        <div className="relative mb-3 h-3 w-full content-start">
          {/* Gradient bar */}
          <div className="absolute left-0 right-0 top-0 z-0 h-3 w-full overflow-hidden rounded-full">
            <Hue
              pointer={() => null}
              className="h-full w-full cursor-pointer"
              {...{ hsl, onChange: handleChange }}
              onChange={handleChange}
              styles={{
                default: {
                  picker: {
                    borderRadius: '1rem',
                    overflow: 'hidden',
                  },
                },
              }}
            />
          </div>
          {/* Custom hue pointer */}
          <div
            className="absolute"
            style={{
              left: `calc(${((hsl?.h || hsl.h) / 360) * 100}% )`,
              top: 0,
              height: 12,
              zIndex: 10,
              pointerEvents: 'none',
              display: 'flex',
              alignItems: 'center',
              transform: 'translateX(-50%)',
            }}
          >
            <div
              className="h-4 w-4 rounded-full border-2"
              style={{
                borderColor: '#fff',
                backgroundColor: 'transparent',
                boxShadow: '0 1px 4px rgba(0,0,0,0.25)',
              }}
            />
          </div>
        </div>

        {/* Hex display */}
        <div className="flex flex-col gap-3">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => {
              const value = e.target.value
              setInputValue(value)
              setErrorMessage(null)

              if (value.length === 7) {
                if (isValidHexColorFormat(value)) {
                  const isValidPosition = getContrastCheckForHexInputColor(value, upperCurve, lowerCurve)

                  if (isValidPosition) {
                    onChange({ hex: value } as ColorResult)
                  } else {
                    setInputValue(lastValidColor.hex)
                    setErrorMessage('This color does not meet the contrast requirements')
                  }
                } else {
                  setErrorMessage('Please enter a valid hex color code (e.g. #FF0000)')
                }
              }
            }}
            className="flex-1 rounded border px-2 py-1 text-sm dark:bg-black-300 dark:text-black-100"
            placeholder="#000000"
          />
          {errorMessage && <span className="text-xs text-red-200">{errorMessage}</span>}
        </div>
      </div>
    )
  },
)
