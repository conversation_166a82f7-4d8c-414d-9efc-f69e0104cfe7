'use client'

import { useEffect, useRef } from 'react'

import { findSaturationBoundaryY } from '@/lib/colors'
import { TColorPickerContrastReferenceColor } from '@/shared-types/types'

interface ContrastCanvasOverlayProps {
  width: number
  height: number
  hue: number
  referenceColors: TColorPickerContrastReferenceColor[]
}

export const ContrastCanvasOverlay = ({ width, height, hue, referenceColors }: ContrastCanvasOverlayProps) => {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return
    const ctx = canvas.getContext('2d')
    if (!ctx) return

    ctx.clearRect(0, 0, width, height)

    const upperCurve: number[] = []
    const lowerCurve: number[] = []
    for (let x = 0; x < width; x++) {
      upperCurve[x] = findSaturationBoundaryY(x, true, height, width, hue, referenceColors)
      lowerCurve[x] = findSaturationBoundaryY(x, false, height, width, hue, referenceColors)
    }

    // Find the last valid x for each curve
    let lastValidUpperX = -1
    let lastValidLowerX = -1

    for (let x = 0; x < width; x++) {
      if (upperCurve[x] < height) {
        lastValidUpperX = x
      }

      if (lowerCurve[x] > 0) {
        lastValidLowerX = x
      }
    }

    // Draw the area above the upper curve (only up to lastValidUpperX)
    if (lastValidUpperX >= 0) {
      ctx.save()
      ctx.beginPath()
      ctx.moveTo(0, 0)
      for (let x = 0; x <= lastValidUpperX; x++) {
        ctx.lineTo(x, upperCurve[x])
      }
      ctx.lineTo(lastValidUpperX, 0)
      ctx.closePath()
      ctx.fillStyle = 'rgba(0,0,0,0.5)'
      ctx.fill()
      ctx.restore()
    }

    // Draw the area below the lower curve (only up to lastValidLowerX)
    if (lastValidLowerX >= 0) {
      ctx.save()
      ctx.beginPath()
      ctx.moveTo(0, height)
      for (let x = 0; x <= lastValidLowerX; x++) {
        ctx.lineTo(x, lowerCurve[x])
      }
      ctx.lineTo(lastValidLowerX, height)
      ctx.closePath()
      ctx.fillStyle = 'rgba(0,0,0,0.5)'
      ctx.fill()
      ctx.restore()
    }
  }, [width, height, hue, referenceColors])

  return (
    <canvas
      ref={canvasRef}
      width={width}
      height={height}
      className="pointer-events-none absolute left-[17px] top-[17px]"
    />
  )
}
