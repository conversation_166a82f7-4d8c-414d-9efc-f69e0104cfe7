import React, { CSSProperties } from 'react'

import { cn } from '@/lib/utils'

interface RippleProps {
  circleScaleFactor?: number
  className?: string
  mainCircleSize?: number
  mainCircleOpacity?: number
  numCircles?: number
}

export const Ripple = React.memo(function Ripple({
  circleScaleFactor = 70,
  mainCircleSize = 210,
  mainCircleOpacity = 0.24,
  numCircles = 8,
  className,
}: RippleProps) {
  return (
    <div
      className={cn(
        'pointer-events-none absolute inset-0 select-none [mask-image:linear-gradient(to_bottom,white,transparent)]',
        className,
      )}
    >
      {/* Center circle with icon */}
      <div
        className="absolute animate-ripple rounded-full border bg-community-primary shadow-xl"
        style={{
          width: `${mainCircleSize}px`,
          height: `${mainCircleSize}px`,
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          zIndex: 1,
          borderColor: `hsl(var(--community-primary)`,
          position: 'relative',
          overflow: 'hidden',
        }}
      />

      {Array.from({ length: numCircles }, (_, i) => {
        const size = mainCircleSize + i * circleScaleFactor
        const opacity = mainCircleOpacity - i * 0.03
        const animationDelay = `${i * 0.06}s`
        const borderOpacity = 5 + i * 5

        return (
          <div
            key={i}
            className={`absolute animate-ripple rounded-full border bg-community-primary/25 shadow-xl [--i:${i}]`}
            style={
              {
                width: `${size}px`,
                height: `${size}px`,
                opacity,
                animationDelay,
                borderStyle: 'solid',
                borderWidth: '1px',
                borderColor: `hsl(var(--community-primary), ${borderOpacity / 100})`,
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%) scale(1)',
                zIndex: 0,
              } as CSSProperties
            }
          />
        )
      })}
    </div>
  )
})

Ripple.displayName = 'Ripple'
