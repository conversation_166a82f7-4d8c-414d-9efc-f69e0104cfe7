import chroma from 'chroma-js'
import { useState } from 'react'

import { ColorPickerPopover } from '../color-picker/color-picker-popover'
import { Input } from './Input'
import { colorPickerContrastReferenceColors } from '@/lib/constants'
import { cn } from '@/lib/utils'

interface ColorInputProps {
  value: string
  onChange: (color: string) => void
  className?: string
}

// TODO: define colors to be used in referenceColors variable (in lib/constants)

export function ColorInput({ className, onChange, value, ...props }: ColorInputProps) {
  const displayValue = chroma(value).hex()
  const [colorPickerOpen, setColorPickerOpen] = useState(false)

  return (
    <div className={cn('flex items-center space-x-2', className)}>
      <div className="-mt-0.5 h-12 w-12 shrink-0 rounded-base" style={{ background: value }} />
      <ColorPickerPopover
        open={colorPickerOpen}
        onChange={(color: string) => onChange(color)}
        setOpen={setColorPickerOpen}
        value={value}
        referenceColors={colorPickerContrastReferenceColors}
      >
        <Input
          {...props}
          value={displayValue}
          onFocus={() => setColorPickerOpen(true)}
          onBlur={() => setColorPickerOpen(false)}
          onChange={(e) => onChange(e.target.value)}
        />
      </ColorPickerPopover>
    </div>
  )
}
