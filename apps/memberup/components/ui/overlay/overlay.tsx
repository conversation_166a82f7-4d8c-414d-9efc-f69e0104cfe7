import * as Portal from '@radix-ui/react-portal'

export const overlayClassName =
  'dialog-overlay fixed inset-0 z-50 bg-black-700/85 backdrop-blur-sm transition-all ease-linear data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 dark:bg-grey-800/85'

export const Overlay = () => (
  <Portal.Root asChild>
    <div className={overlayClassName} />
  </Portal.Root>
)
