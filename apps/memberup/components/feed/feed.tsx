'use client'

import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'

import { CommunityDetails } from '@/components/community/community-details'
import { PostListing } from '@/components/feed/post-listing'
import { useStore } from '@/hooks/useStore'
import { setFeedTrackIds } from '@/memberup/store/features/feedTrackSlice'
import { useAppDispatch } from '@/memberup/store/store'
import { getFeedTracks } from '@/shared-services/apis/feed-track.api'

export function Feed() {
  const dispatch = useAppDispatch()
  const searchParams = useSearchParams()
  const spaceParam = searchParams.get('space')
  const user = useStore((state) => state.auth.user)
  const membership = useStore((state) => state.community.membership)

  let selectedSpaceInfo = membership?.channels.find((c) => c.slug === spaceParam)

  if (!selectedSpaceInfo) {
    selectedSpaceInfo = { id: 'community', slug: 'community' } as any
  }

  useEffect(() => {
    async function fetchFeedTracks() {
      if (user) {
        const feedTracksMap = await getFeedTracks()
        dispatch(setFeedTrackIds(feedTracksMap))
      }
    }

    fetchFeedTracks()
  }, [dispatch, user])

  if (!membership) {
    return
  }

  return (
    <div className="space-y:6 page-inner-pb flex flex-col-reverse items-start md:flex-row md:space-x-6 md:space-y-0">
      <div className="mobile-padded-content-container relative w-full" id="home">
        <PostListing />
      </div>
      <CommunityDetails />
    </div>
  )
}
