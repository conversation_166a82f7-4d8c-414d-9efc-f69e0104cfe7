import { useMediaQuery } from '@mui/material'
import Box from '@mui/material/Box'
import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import CardMedia from '@mui/material/CardMedia'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import { makeStyles } from '@mui/styles'
import clsx from 'clsx'
import Image from 'next/image'

import SVGNewLink from '../../src/components/svgs/new-link'
import { cropString } from '@/shared-libs/string-utils'

const useStyles = makeStyles((theme) => ({
  container: {
    backgroundColor: theme.palette.mode == 'dark' ? '#29292c !important' : '#edeeef',
    height: '120px',
    display: 'flex',
    opacity: 1,
    borderRadius: '12px',
    marginBottom: '2px',
    marginTop: '2px',
    color: theme.palette.mode == 'dark' ? 'rgba(255,255,255,0.87)' : '#17171a',
  },
  title: {
    opacity: 1,
    fontFamily: '"Graphik Semibold"',
    fontSize: 14,
    fontWeight: 600,
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'left',
    lineHeight: '16px',
    marginBottom: '8px',
  },

  description: {
    opacity: 1,
    fontFamily: '"Graphik Regular"',
    fontSize: 13,
    fontWeight: 400,
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'left',
    lineHeight: '20px',
    marginBottom: '8px',
    height: '40px', // Show just 2 lines at most
    overflow: 'hidden',
  },
  thumbnailContainer: {
    position: 'relative',
    width: 'fit-content' /* This ensures the container fits around the image. */,
    display: 'inline-block' /* So it doesn't take up the full width of its container. */,
  },
  overlayIcon: {
    position: 'absolute',
    top: 0,
    right: 0,
    zIndex: 1,
  },
  url: {
    opacity: 1,
    fontFamily: '"Graphik Semibold"',
    fontSize: 14,
    fontWeight: 600,
    fontStyle: 'normal',
    letterSpacing: 0,
    textAlign: 'left',
    lineHeight: '20px',
    marginBottom: '8px',
    marginTop: '8px',
  },
}))

const LinkPreview: React.FC<{
  title?: string
  description?: string
  thumbnail?: any
  url: string
  disabled?: boolean
  className?: string
}> = ({ title, description, thumbnail, url, disabled, className }) => {
  const classes = useStyles()
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  const handleLinkClick = () => {
    window.open(url, '_blank')
  }
  return (
    <>
      <Card className={clsx(classes.container, className)}>
        {!thumbnail && (
          <Box
            sx={{
              cursor: !disabled ? 'pointer' : '',
              width: '120px',
              borderRadius: '12px',
              overflow: 'hidden',
              position: 'relative',
              backgroundColor: theme.palette.mode == 'dark' ? '#303033' : '#e7e9ea',
            }}
            onClick={() => {
              handleLinkClick()
            }}
          >
            <Box
              sx={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                color: '#fff',
                transform: 'translate(-50%, -50%)',
                zIndex: 1,
              }}
            >
              <Image src="/assets/default/images/icons/link.png" alt="link icon" width={29} height={23} />
            </Box>

            <IconButton
              sx={{
                position: 'absolute',
                top: 0,
                right: 0,
                zIndex: 2,
                backgroundColor: 'transparent',
                borderRadius: '8px',
                padding: '4px',
              }}
              color="primary"
            >
              <SVGNewLink />
            </IconButton>
          </Box>
        )}
        {thumbnail && (
          <div className={classes.thumbnailContainer}>
            <CardMedia
              component="img"
              sx={{
                width: 120,
                height: 120,
                borderRadius: '12px',
                cursor: disabled ? '' : 'pointer',
                objectFit: 'cover',
                objectPosition: 'top left',
              }}
              image={thumbnail?.url || thumbnail}
              alt={title}
              onClick={handleLinkClick}
            />
            {!disabled && (
              <Box
                sx={{
                  position: 'absolute',
                  top: 0,
                  right: 0,
                  zIndex: 1,
                  backgroundColor: 'transparent',
                  borderRadius: '8px',
                  padding: '4px',
                  cursor: 'pointer',
                }}
                onClick={handleLinkClick}
                color="primary"
                aria-label="link"
              >
                <SVGNewLink />
              </Box>
            )}
          </div>
        )}

        <CardContent sx={{ flex: 1, padding: '16px !important' }}>
          <Typography component="div" variant="h5" className={classes.title}>
            {title || cropString(url, isMobile ? 22 : 55)}
          </Typography>
          <Box color="text.secondary" component="div" className={classes.description}>
            {description}
          </Box>
          <Typography variant="subtitle1" color="text.secondary" component="div" className={classes.url}>
            {disabled ? (
              <span>{new URL(url).hostname}</span>
            ) : (
              <a href={url} style={{ color: 'inherit' }}>
                {new URL(url).hostname}
              </a>
            )}
          </Typography>
        </CardContent>
      </Card>
    </>
  )
}

export default LinkPreview
