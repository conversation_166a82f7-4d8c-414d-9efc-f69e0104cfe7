'use client'

import LinkIcon from '@mui/icons-material/Link'
import { Theme } from '@mui/material'
import Box from '@mui/material/Box'
import CardMedia from '@mui/material/CardMedia'
import Grid from '@mui/material/Grid'
import IconButton from '@mui/material/IconButton'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import useMediaQuery from '@mui/material/useMediaQuery'
import { makeStyles } from '@mui/styles'
import Image from 'next/image'
import React from 'react'

import { useRenderTextWithMentions } from '../../src/components/hooks/use-render-with-mentions'
import SVGMultiPhoto from '../../src/components/svgs/multiphoto'
import SVGNewLink from '../../src/components/svgs/new-link'
import SVGNewPlay from '../../src/components/svgs/new-play'
import { GiphyGif } from '@/components/images/giphy-gif'
import { useIsUserProfilePathname } from '@/hooks/useIsUserProfilePathname'
import { cn } from '@/lib/utils'
import { isUUIDv4 } from '@/memberup/libs/utils'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { excludedFaviconDomains } from '@/shared-libs/image'

type StyleProps = {
  lines: number
}

const useStyles = makeStyles<Theme, StyleProps>(() => ({
  truncateText: ({ lines }) => ({
    display: '-webkit-box',
    '-webkit-line-clamp': `${lines}`,
    '-webkit-box-orient': 'vertical',
    overflow: 'hidden',
    fontSize: '14px',
    fontWeight: '400',
    lineHeight: '1.5rem',
  }),
}))

const THUMBNAIL_SIZE = 120

function CondensedViewThumbnail({ thumbnail, attachmentsCount }) {
  const theme = useTheme()
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))

  const getCloudinaryResizedImage = (url: string, width: number) => {
    if (!url.includes('res.cloudinary')) {
      return url
    }
    return url.replace('/image/upload/', `/image/upload/w_${width * 3},f_auto,q_70/`)
  }

  if (!thumbnail || (thumbnail.mimetype === 'video' && !(thumbnail.thumbnail || thumbnail.thumb_url))) {
    return (
      <Box
        className="bg-white-300 dark:bg-grey-800"
        data-testid={'post-thumbnail'}
        sx={{
          cursor: 'pointer',
          borderRadius: '12px',
          width: { xs: '80px', sm: '120px' },
          overflow: 'hidden',
          position: 'relative',
        }}
      >
        <Box
          sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            color: '#fff',
            transform: 'translate(-50%, -50%)',
            zIndex: 1,
          }}
        >
          {!thumbnail && <Image src="/assets/default/images/icons/link.png" alt="link icon" width={29} height={23} />}

          {thumbnail && <Image src="/assets/default/images/icons/video.png" alt="link icon" width={36} height={48} />}
        </Box>

        {!thumbnail && (
          <IconButton
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              zIndex: 2,
              backgroundColor: 'transparent',
              borderRadius: '8px',
              padding: '4px',
            }}
            color="primary"
            aria-label="link"
          >
            <SVGNewLink />
          </IconButton>
        )}
      </Box>
    )
  }

  return (
    <div
      style={{
        position: 'relative',
        width: 'fit-content',
        display: 'inline-block',
      }}
    >
      {!thumbnail && (
        <Box
          className="bg-white-300 dark:bg-grey-800"
          sx={{
            cursor: 'pointer',
            borderRadius: '12px',
            width: { xs: '80px', sm: '120px' },
            height: { xs: '80px', sm: '120px' },
            overflow: 'hidden',
            position: 'relative',
          }}
        >
          <LinkIcon sx={{ color: 'white', backgroundColor: 'rgba(88,93,102, 0.6)' }} />
        </Box>
      )}

      {(thumbnail.type === 'image' || thumbnail.type === 'video') && (
        <>
          <CardMedia
            component="img"
            sx={{
              width: { xs: '80px', sm: '120px' },
              height: { xs: '80px', sm: '120px' },
              borderRadius: '12px',
              objectFit: 'cover',
              objectPosition: 'top left',
            }}
            image={getCloudinaryResizedImage(thumbnail.thumb_url, THUMBNAIL_SIZE)}
            alt="Thumbnail"
          />
          <IconButton
            sx={{
              position: 'absolute',
              top: 0,
              right: 0,
              zIndex: 1,
              backgroundColor: 'transparent',
              borderRadius: '8px',
              padding: '4px',
            }}
            color="primary"
            aria-label="link"
          >
            <SVGNewLink />
          </IconButton>
        </>
      )}

      {(thumbnail.mimetype === 'image' || thumbnail.mimetype === 'pdf') && (
        <Box sx={{ position: 'relative' }}>
          <CardMedia
            component="img"
            sx={{
              width: { xs: '80px', sm: '120px' },
              height: { xs: '80px', sm: '120px' },
              borderRadius: '12px',
              objectFit: 'cover',
              objectPosition: thumbnail.mimetype === 'pdf' ? 'top left' : 'center',
            }}
            image={getCloudinaryResizedImage(
              thumbnail.thumbnail?.toString() || thumbnail.thumb_url?.toString() || thumbnail.url?.toString(),
              THUMBNAIL_SIZE,
            )}
            alt="Thumbnail"
          />
          {thumbnail.mimetype === 'pdf' && (
            <Image
              src="/assets/default/images/PDFNew.svg"
              style={{ position: 'absolute', bottom: 4, right: 4 }}
              alt="pdf icon"
              width={32}
              height={32}
            />
          )}
        </Box>
      )}

      {thumbnail.mimetype === 'video' && (thumbnail.thumbnail || thumbnail.thumb_url) && (
        <>
          <CardMedia
            component="img"
            sx={{
              width: { xs: '80px', sm: '120px' },
              height: { xs: '80px', sm: '120px' },
              borderRadius: '12px',
            }}
            image={thumbnail.thumbnail || thumbnail.thumb_url}
            alt="Thumbnail"
          />
          <IconButton
            sx={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              backgroundColor: 'transparent',
              zIndex: 1,
            }}
            aria-label="play video"
          >
            <SVGNewPlay />
          </IconButton>
        </>
      )}

      {thumbnail.mimetype === 'gif' && (
        <Box
          sx={{
            overflow: 'hidden',
            borderRadius: '12px',
            height: isMobile ? 80 : 120,
            '& img': {
              height: '100%',
            },
          }}
        >
          <GiphyGif
            id={thumbnail.id}
            width={isMobile ? 80 : 120}
            className="h-[80px] w-[80px] lg:h-[120px] lg:w-[120px]"
          />
        </Box>
      )}
      {attachmentsCount > 1 && (
        <div
          style={{
            position: 'absolute',
            bottom: 8,
            right: 8,
            top: thumbnail.mimetype === 'pdf' ? 10 : '',
            backgroundColor: 'rgba(88,93,102, 0.6)',
            color: 'white',
            padding: '8px',
            display: 'flex',
            alignItems: 'center',
            fontSize: '12px',
            borderRadius: '15px',
            width: '48px',
            height: '26px',
            justifyContent: 'space-around',
          }}
        >
          <SVGMultiPhoto />
          <span>{attachmentsCount}</span>
        </div>
      )}
    </div>
  )
}

const isThumbnailInExcludedDomains = (thumbnail: string) => {
  return excludedFaviconDomains.some((domain) => thumbnail?.includes(domain))
}

const selectThumbnail = (feed) => {
  /* the link is no longer being added to the attachments array, but we need to give support to old posts and that's why we need to consider links may come in the attachments property as well as images */
  const linksCount = feed?.links ? Object.keys(feed?.links).length : 0
  let hasOtherFilesApartFromLinks = false
  if (feed.links) {
    hasOtherFilesApartFromLinks = feed.attachments?.length > linksCount
  }

  if (hasOtherFilesApartFromLinks || !linksCount) {
    let realAttachments = getNonLinksAttachments(feed)
    let selectedThumbnail = findThumbnailInAttachments(realAttachments)

    if (selectedThumbnail) {
      return selectedThumbnail
    }
  }

  if (linksCount) {
    const displayableLinks = getDisplayableLinks(feed)
    if (displayableLinks.length > 0) {
      return { type: 'image', thumb_url: displayableLinks[0] }
    }
  }

  return null
}

const getNonLinksAttachments = (feed) => {
  let realAttachments = feed.attachments

  if (feed.links) {
    realAttachments = feed.attachments.filter(
      (attachment) =>
        (attachment.title_link || attachment.url) && !feed.links[attachment.title_link] && !feed.links[attachment.url],
    )
  }

  return realAttachments
}

const findThumbnailInAttachments = (attachments) => {
  return attachments.find(
    (attachment) =>
      (attachment.mimetype === 'image' && (attachment.thumbnail || attachment.thumb_url || attachment.url)) ||
      (attachment.mimetype === 'video' && (attachment.thumbnail || attachment.thumb_url)) ||
      attachment.mimetype === 'gif' ||
      (attachment.mimetype === 'pdf' &&
        attachment.thumbnail &&
        (attachment.show_preview === undefined || attachment.show_preview)),
  )
}

const getDisplayableLinks = (feed) => {
  const filteredLinkAttachments = Object.values(feed.links).filter((linkObj: any) => {
    if (linkObj.thumbnail?.url && !isThumbnailInExcludedDomains(linkObj.thumbnail?.url)) {
      return true
    } else if (linkObj.thumbnail && !isThumbnailInExcludedDomains(linkObj.thumbnail)) {
      return true
    }
    return false
  })

  return filteredLinkAttachments.map((link: any) => {
    return link.thumbnail?.url || link.thumbnail
  })
}

export function PostSummaryContent({ feed, hasPostBeenRead = false }: { feed: any; hasPostBeenRead?: boolean }) {
  const theme = useTheme()
  // We only display the link thumbnail if there is only one link in the post and just 1 attachment since the link comes as attachment as well from the stream
  const linksCount = feed?.links ? Object.keys(feed?.links).length : 0
  const attachmentsCount = feed.attachments?.length - linksCount
  const members = useAppSelector((state) => selectMembersMap(state))
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const renderedText = useRenderTextWithMentions(feed.text, feed?.mentioned_users, members, 'condensed', feed)
  const isUserProfilePathname = useIsUserProfilePathname()

  // to prioritize other files than links
  let hasOtherFilesApartFromLinks = false
  if (feed.links) {
    hasOtherFilesApartFromLinks = feed.attachments?.length > Object.keys(feed.links)?.length
  }
  const selectedThumbnail = selectThumbnail(feed)

  const lines = selectedThumbnail ? 3 : 2
  const classes = useStyles({ lines })

  return (
    <Grid item xs={12} sx={{ mt: 1 }}>
      {
        <Box data-testid="post-content">
          <Grid
            sx={{
              display: 'flex',
              minHeight: 'auto',
              pb: (selectedThumbnail && !isMobile) || (linksCount > 0 && !isMobile) ? '0px' : '20px',
              flexGrow: '1',
            }}
          >
            <Grid
              item
              sx={{
                display: 'block',
                pr: selectedThumbnail || linksCount > 0 ? '16px' : 0,
                flexGrow: 1,
              }}
            >
              <Grid container columnSpacing={1} sx={{ postion: 'relative' }} alignItems="center">
                {!isUUIDv4(feed.title) && (
                  <Grid item xs={12}>
                    <Typography
                      variant="subtitle1"
                      gutterBottom
                      className="text-semibold text-black-700 dark:text-white-500"
                      style={{
                        lineHeight: '24px',
                        marginBottom: '10px',
                        overflow: 'hidden',
                        WebkitLineClamp: isMobile ? 2 : 1,
                        display: '-webkit-box',
                        WebkitBoxOrient: 'vertical',
                        textOverflow: 'ellipsis',
                      }}
                    >
                      {!hasPostBeenRead && feed.title && (
                        <div
                          className={cn(
                            'mr-2 inline-block h-[0.5625rem] w-[0.5625rem] rounded-full',
                            isUserProfilePathname ? 'bg-primary-100' : 'bg-community-primary',
                          )}
                        />
                      )}
                      {feed.title as string}
                    </Typography>
                  </Grid>
                )}
              </Grid>
              <div className={cn(classes.truncateText, 'text-sm leading-5 text-black-600 dark:text-white-200')}>
                {renderedText}
              </div>
            </Grid>
            {(selectedThumbnail || linksCount > 0) && (
              <Grid item sx={{ pr: selectedThumbnail ? '4px' : 0 }} className="shrink-0">
                <CondensedViewThumbnail thumbnail={selectedThumbnail} attachmentsCount={attachmentsCount} />
              </Grid>
            )}
          </Grid>
        </Box>
      }
    </Grid>
  )
}
