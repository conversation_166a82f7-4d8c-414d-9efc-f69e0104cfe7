import { Comment } from './comment'
import { cn } from '@/lib/utils'
import { IComment, IMembership } from '@/shared-types/interfaces'

export function CommentThread({
  className,
  comment,
  membership,
}: {
  className?: string
  comment: IComment
  membership: IMembership
}) {
  return (
    <div className={cn('comment-thread tailwind-component', className)}>
      <Comment membership={membership} commentData={comment} members={{}} />
      {comment.replies && comment.replies.length > 0 && (
        <div className="ml-[3.25rem] flex flex-col space-y-4 pt-4">
          {comment.replies.map((reply) => (
            <Comment membership={membership} key={reply.id} commentData={reply} members={{}} />
          ))}
        </div>
      )}
    </div>
  )
}
