import React from 'react'

import SVGPushPinLargeFilled from '../../src/components/svgs/push-pin-large-filled'
import { Button } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { hidePinnedPostApi } from '@/shared-services/apis/user.api'

export default function UndoHidePinnedPost({ postId, onUndo }: { postId: string; onUndo: () => void }) {
  const [requestUnhide, setrequestUnhide] = React.useState(false)
  const { updateProfile } = useStore((state) => state.auth)

  const handleUndo = async () => {
    setrequestUnhide(true)
    const result = await hidePinnedPostApi({ hide: false, post_id: postId })
    if (result.data.success) {
      updateProfile({ pinned_posts_hidden: result.data.data })
    }
    onUndo()
    setrequestUnhide(false)
  }

  return (
    <div className="bg-white mb-4 mt-4 flex items-center rounded-2xl bg-white-500 px-4 py-4 dark:bg-black-500">
      <SVGPushPinLargeFilled className="mr-4 shrink-0" />
      <div className="flex flex-grow flex-row">
        <div className="flex flex-col items-start">
          <p className="text-black dark:text-white mb-1 text-left text-sm font-medium leading-5 opacity-100">
            Post hidden
          </p>
          <p className="text-left text-sm font-normal leading-5 text-[#8D94A3]">
            You won&apos;t see this post in your Community Feed.
          </p>
        </div>
      </div>
      <Button
        className="ml-4"
        disabled={requestUnhide}
        loading={requestUnhide}
        onClick={handleUndo}
        size="sm"
        variant="community-primary"
      >
        Undo
      </Button>
    </div>
  )
}
