'use client'

import Link from 'next/link'
import { Mouse<PERSON><PERSON>, ReactNode, useState } from 'react'
import { useSelector } from 'react-redux'
import { StreamMessage } from 'stream-chat-react'

import { PostSummaryContent } from './post-summary-content'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import { CommentThread } from '@/components/feed/comment-thread'
import { PostDetailDialog } from '@/components/feed/post-detail-dialog'
import { PostHeader } from '@/components/feed/post-header'
import { PostSocialBar } from '@/components/feed/post-social-bar'
import { Pin16Icon } from '@/components/icons/16px/pin-16-icon'
import { Button, Separator } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { cn } from '@/lib/utils'
import { selectMembersMap } from '@/memberup/store/features/memberSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { RootState } from '@/memberup/store/store'
import { isUserActiveAndAcceptedInCommunity } from '@/shared-libs/profile'
import { hidePinnedPostApi } from '@/shared-services/apis/user.api'
import { IMembership } from '@/shared-types/interfaces'

interface PostSummaryProps {
  className?: string
  feed: StreamMessage
  showPinnedMessageIndicator?: boolean
  comments?: IFeed[]
  membership?: IMembership
  extraHeaderComponents?: ReactNode
  readonly?: boolean
  onPinMessage?: (message: StreamMessage) => Promise<void>
  onUnpinMessage?: (message: StreamMessage) => Promise<void>
  onPinnedPostHide?: (postId: string) => void
  onPinnedPostShow?: (postId: string) => void
}

export function PostSummary({
  className,
  feed: message,
  showPinnedMessageIndicator = true,
  comments,
  membership,
  onPinnedPostHide,
  onPinnedPostShow,
  onPinMessage,
  onUnpinMessage,
  extraHeaderComponents,
  readonly,
}: PostSummaryProps) {
  const user = useStore((state) => state.auth.user)
  const userProfile = useStore((state) => state.auth.profile)
  const updateProfile = useStore((state) => state.auth.updateProfile)
  const [postDetailsDialogOpen, setPostDetailsDialogOpen] = useState(false)
  const [hideRequest, setHideRequest] = useState(false)

  const isUserAllowedToPost = isUserActiveAndAcceptedInCommunity(user, membership)

  const members = useAppSelector((state) => selectMembersMap(state))
  const feedTrackIds = useSelector((state: RootState) => state.feedTrack.feedTrackIds)
  const isPostOwner = message.user.id === userProfile?.user_id
  const feed = message

  const latestCommentTrackDate = feedTrackIds?.[feed.id] ? feedTrackIds[feed.id] : null
  const hasCommentsUnread = message.latest_comment_timestamp > latestCommentTrackDate

  const hasPostBeenRead = feedTrackIds?.[feed.id] || isPostOwner
  const isHidden = Boolean(userProfile?.pinned_posts_hidden?.[feed.id])
  const showPinnedPostBar = showPinnedMessageIndicator && feed.pinned

  const openPostDetailDialog = () => {
    setPostDetailsDialogOpen(true)
  }

  const closePostDetailDialog = () => {
    setPostDetailsDialogOpen(false)
  }

  const changePinnedPostVisibility = async (hide: boolean, postId: string) => {
    setHideRequest(true)
    try {
      const result = await hidePinnedPostApi({ hide, post_id: postId })
      if (result.data.success) {
        updateProfile({ pinned_posts_hidden: result.data.data })
        if (hide) {
          onPinnedPostHide?.(feed.id)
        } else {
          onPinnedPostShow?.(feed.id)
        }
      }
    } catch (e) {
      toast.error(e.message)
    } finally {
      setHideRequest(false)
    }
  }

  const PostContentWrapper = ({ children }: { children: ReactNode }) => {
    return !readonly ? (
      <Link
        href={`${membership.slug}/post/${feed.permalink || feed.id}`}
        onClick={(e) => {
          e.preventDefault()
          e.stopPropagation()
          openPostDetailDialog()
        }}
      >
        {children}
      </Link>
    ) : (
      children
    )
  }

  const handlePinnedStatusChange = (e: MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation()
    e.preventDefault()

    if (isHidden) {
      changePinnedPostVisibility(false, feed.id)
    } else {
      changePinnedPostVisibility(true, feed.id)
    }
  }

  return (
    <div
      className={cn('post-summary relative overflow-hidden rounded-base bg-white-500 dark:bg-black-500', className)}
      onClick={
        readonly
          ? undefined
          : (e) => {
              e.preventDefault()
              openPostDetailDialog()
            }
      }
    >
      {showPinnedPostBar && (
        <div
          className="flex h-6 justify-between px-5 text-xs"
          style={{
            background:
              'linear-gradient(90deg, hsl(var(--community-primary) / 1) 0%, hsl(var(--community-secondary) / 0.1) 100%)',
          }}
        >
          <div className="flex items-center font-semibold">
            <Pin16Icon className="-ml-0.5 mr-0.5" />
            <div>Pinned Post</div>
          </div>
          {user && (
            <Button
              className="text-xs font-semibold text-black-200"
              variant="inline"
              onClick={handlePinnedStatusChange}
              disabled={hideRequest}
            >
              {isHidden ? 'Show' : 'Hide'}
            </Button>
          )}
        </div>
      )}
      <div className="p-5">
        <PostHeader
          className="mb-4"
          feed={feed as unknown as IFeed}
          isSinglePost={false}
          isPostPage={false}
          userData={members[feed.user.id]}
          showPinnedMessageIndicator={showPinnedMessageIndicator}
          extraHeaderComponents={extraHeaderComponents}
          membership={membership}
        />
        <PostContentWrapper>
          <PostSummaryContent feed={feed} hasPostBeenRead={hasPostBeenRead} />
          <PostSocialBar
            readonly={readonly || !isUserAllowedToPost}
            message={feed as unknown as IFeed}
            isDetailView={false}
            hasCommentsUnread={hasCommentsUnread}
            members={members}
          />
        </PostContentWrapper>
      </div>
      {comments && comments.length > 0 && (
        <div className="feed-card-condensed-comments px-5 pb-5">
          <Separator className="mb-5" />
          {comments.map((comment: IFeed) => (
            <CommentThread membership={membership} className="mb-4 last:mb-0" comment={comment} key={comment.id} />
          ))}
        </div>
      )}
      {postDetailsDialogOpen && (
        <PostDetailDialog
          membership={membership}
          open={true}
          feed={feed}
          onClose={closePostDetailDialog}
          onPinMessage={onPinMessage}
          onUnpinMessage={onUnpinMessage}
        />
      )}
    </div>
  )
}
