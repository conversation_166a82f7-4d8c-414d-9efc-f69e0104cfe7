import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Skeleton from '@mui/material/Skeleton'
import Stack from '@mui/material/Stack'
import useTheme from '@mui/material/styles/useTheme'
import Typography from '@mui/material/Typography'
import React from 'react'

const PostSkeleton = (props) => {
  const { message } = props
  const theme = useTheme()
  return (
    <div className={'mt-5 w-full'}>
      <Card
        className="bg-white-700 dark:bg-black-500"
        data-cy={'no-post'}
        sx={{
          marginBottom: '16px',
          borderRadius: '8px',
          padding: '12px',
          width: '100%',
          '& .MuiCardContent-root': {
            width: '100% !important',
          },
        }}
      >
        <CardContent style={{ padding: '8px', paddingBottom: 0 }}>
          <Stack spacing={2}>
            <Stack direction="row" alignItems="center" spacing={3} sx={{ mb: '40px' }}>
              <Skeleton variant="circular" width={36} height={36} animation={false} />
              <Stack spacing={2}>
                <Skeleton variant="rounded" width={90} height={12} animation={false} />
                <Skeleton variant="rounded" width={116} height={12} animation={false} />
              </Stack>
            </Stack>
            <Typography
              sx={{
                fontSize: '14px',
                margin: 'auto',
                textAlign: 'center',
                color: theme.palette.mode === 'dark' ? '#89909e' : '#6e6e6e',
              }}
            >
              {message}
            </Typography>
            <Stack sx={{ marginTop: '60px !important' }} direction="row" alignItems="center" spacing={3}>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Skeleton variant="circular" width={24} height={24} animation={false} />
                <Skeleton variant="rounded" width={54} height={10} animation={false} />
              </Stack>
              <Stack direction="row" alignItems="center" spacing={2}>
                <Skeleton variant="circular" width={24} height={24} animation={false} />
                <Skeleton variant="rounded" width={72} height={10} animation={false} />
              </Stack>
            </Stack>
          </Stack>
        </CardContent>
      </Card>
    </div>
  )
}

export default React.memo(PostSkeleton)
