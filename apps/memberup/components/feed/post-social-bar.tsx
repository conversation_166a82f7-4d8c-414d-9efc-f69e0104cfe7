import { usePostHog } from 'posthog-js/react'
import React, { useMemo } from 'react'

import SVGCommentNew from '../../src/components/svgs/comment-new'
import { AppProfileImage } from '@memberup/shared/src/components/common/profile-image'
import { getDateTimeFromNow } from '@memberup/shared/src/libs/date-utils'
import { IFeed } from '@memberup/shared/src/types/interfaces'
import { Button } from '@/components/ui'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import useFeedLike from '@/hooks/feed/use-feed-like'
import { useIsUserProfilePathname } from '@/hooks/useIsUserProfilePathname'
import { FeedEvents } from '@/lib/posthog'
import { cn } from '@/lib/utils'
import SVGHeart from '@/memberup/components/svgs/heart'
import SVGHeartFilled from '@/memberup/components/svgs/heart-filled'
import { selectLikes } from '@/memberup/store/features/likesSlice'
import { useAppSelector } from '@/memberup/store/hooks'
import { IUser } from '@/shared-types/interfaces'

const PostSocialBar: React.FC<{
  className?: string
  readonly: boolean
  message: IFeed
  hasCommentsUnread: boolean
  isDetailView: boolean
  members: { [key: string]: IUser }
}> = ({ className, readonly, message, hasCommentsUnread, isDetailView, members }) => {
  const posthog = usePostHog()

  const isUserProfilePathname = useIsUserProfilePathname()
  const featuredCommenters = useMemo(() => (message['featured_commenters'] || []).filter((f: IUser) => f), [message])
  const likesData = useAppSelector((state) => selectLikes(state))

  const memoizedFeaturedCommenters = useMemo(() => {
    return featuredCommenters
      .reverse()
      .map((com: IUser) => ({
        id: com.id,
        image: com.image || members?.[com.id]?.profile?.image,
        image_crop_area: com.image_crop_area || members?.[com.id]?.profile?.image_crop_area,
        name: members?.[com.id]?.name || com.name,
      }))
      .filter((f) => f !== null)
  }, [featuredCommenters, members])

  const { handleLike, loading } = useFeedLike()

  const liked = likesData?.[message.id]
    ? likesData[message.id].liked
    : Boolean(message.own_reactions.find((item) => item.type === 'like'))
  const likeCount = likesData?.[message.id] ? likesData[message.id].likeCount : message?.reaction_counts?.like || 0

  // Optimistic UI updates for liking/unliking
  const handleLikeClick = () => {
    if (loading) return

    const status = liked ? 'unliked' : 'liked'

    handleLike(message, status)

    posthog.capture(status === 'liked' ? FeedEvents.POST_LIKED : FeedEvents.POST_UNLIKED, {
      post_id: message.id,
      user_id: message.user_id,
    })
  }

  const messageCount = message.reply_count ?? 0
  const commentText = hasCommentsUnread ? 'New comment' : 'Last comment'
  const commentTime = getDateTimeFromNow(message.latest_comment_timestamp * 1000)

  return (
    <div className={cn('flex gap-8 px-4 md:px-0', className)}>
      <div className="flex items-center gap-2 text-ssm font-semibold text-black-700 dark:text-white-200">
        {!readonly ? (
          <Button
            className="text-black-700 dark:text-white-200"
            variant="inline"
            aria-label={liked ? 'Unlike post' : 'Like post'}
            aria-pressed={liked}
            onClick={(e) => {
              e.preventDefault()
              e.stopPropagation()
              handleLikeClick()
            }}
          >
            {liked ? <SVGHeartFilled className="text-community-primary" /> : <SVGHeart />}
          </Button>
        ) : (
          <SVGHeart />
        )}
        <span className="flex items-center justify-center align-middle leading-none">{likeCount}</span>
      </div>

      <div className="flex flex-row items-center gap-2 text-ssm font-semibold text-black-700 dark:text-white-200">
        {!readonly ? (
          <Button variant="inline" disabled={true} className="text-black-700 dark:text-white-200">
            <SVGCommentNew />
          </Button>
        ) : (
          <SVGCommentNew />
        )}
        <span className="flex items-center justify-center align-middle leading-none">{messageCount}</span>
      </div>

      {message.latest_comment_timestamp && !isDetailView && (
        <div className="flex flex-row items-center">
          <div className="flex-grow-1">
            {Boolean(memoizedFeaturedCommenters?.length) && (
              <div className="mr-4 hidden -space-x-[0.5625rem] md:flex">
                {memoizedFeaturedCommenters.slice().map((item) => (
                  <Popover key={item.id}>
                    <PopoverTrigger asChild>
                      <AppProfileImage
                        imageUrl={item.image}
                        cropArea={item.image_crop_area}
                        name={item.name}
                        size={24}
                      />
                    </PopoverTrigger>
                    <PopoverContent>
                      <div>{item.name}</div>
                    </PopoverContent>
                  </Popover>
                ))}
              </div>
            )}
          </div>
          <div
            className={cn(
              'flex-grow-1 text-right text-xs font-medium leading-6',
              hasCommentsUnread
                ? isUserProfilePathname
                  ? 'text-primary-100'
                  : 'text-community-primary'
                : 'text-black-200 dark:text-black-100',
            )}
            style={{
              verticalAlign: 'middle',
              marginTop: '0px',
            }}
          >
            <div className="inline-flex items-center">
              <span className="hidden md:inline">{commentText}&nbsp;</span> {commentTime}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export { PostSocialBar }
