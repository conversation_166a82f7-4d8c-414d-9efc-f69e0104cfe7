import { makeStyles } from '@mui/styles'
import ReactPlayer from 'react-player/lazy'

import LinkPreview from '@/components/feed/link-preview'
import { IFeed } from '@/shared-types/interfaces'

const useStyles = makeStyles(() => ({
  player: {
    borderRadius: '16px',
    overflow: 'hidden',
    marginBottom: '8px',
  },
  linkPreview: {
    marginBottom: '12px',
  },
}))

type LinkData = {
  title?: string
  description?: string
  thumbnail?: string
}

export default function PostLinks({ post }: { post: IFeed }) {
  const classes = useStyles()
  const { links } = post

  return (
    links && (
      <div>
        {Object.entries(links).map(([url, linkData]: [string, LinkData]) => {
          if (ReactPlayer.canPlay(url)) {
            return (
              <ReactPlayer key={url} width={'100%'} className={classes.player} light={true} controls={true} url={url} />
            )
          } else {
            return (
              <LinkPreview
                key={url}
                disabled={false}
                url={url}
                title={linkData.title}
                description={linkData.description}
                thumbnail={linkData.thumbnail}
                className={classes.linkPreview}
              />
            )
          }
        })}
      </div>
    )
  )
}
