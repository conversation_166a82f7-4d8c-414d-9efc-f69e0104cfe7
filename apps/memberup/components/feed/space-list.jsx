import AddIcon from '@mui/icons-material/Add'
import MoreHorizIcon from '@mui/icons-material/MoreHoriz'
import { Typography } from '@mui/material'
import Box from '@mui/material/Box'
import List from '@mui/material/List'
import ListItemButton from '@mui/material/ListItemButton'
import ListItemIcon from '@mui/material/ListItemIcon'
import ListItemText from '@mui/material/ListItemText'
import { makeStyles, useTheme } from '@mui/styles'
import clsx from 'clsx'
import PropTypes from 'prop-types'
import { useSelector } from 'react-redux'

import SVGHashtag from '../../src/components/svgs/hashtag'
import { useAppDispatch, useAppSelector } from '../../src/store/hooks'
import { SPACE_TYPE_ENUM } from '@memberup/shared/src/types/enum'
import { filterSpaces } from '@/lib/utils'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import SVGHome from '@/memberup/components/svgs/home'
import { getCommunityBaseURL } from '@/memberup/libs/utils'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { selectActiveChannels } from '@/src/store/features/spaceSlice'
import { selectUser } from '@/src/store/features/userSlice'

const useStyles = makeStyles((theme) => ({
  lineConnector: {
    position: 'absolute',
    top: 0,
    left: '18px', // adjust this value as needed
    height: '43px',
    width: '10px',
    backgroundRepeat: 'repeat-y',
    background: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 110' width='10' height='100'%3E%3Cline x1='5' y1='1' x2='5' y2='46' stroke='${encodeURIComponent(
      theme.palette.mode === 'dark' ? '#2a2b2f' : '#e0e2e3',
    )}' stroke-width='2.2' stroke-linecap='round'/%3E%3C/svg%3E")`,
    borderRadius: '30px',
  },
  lastLineConnector: {
    position: 'absolute',
    top: 0,
    left: '18px', // adjust this value as needed
    height: '35px',
    width: '10px',
    backgroundRepeat: 'repeat-y',
    background: `url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 110' width='10' height='100'%3E%3Cline x1='5' y1='1' x2='5' y2='46' stroke='${encodeURIComponent(
      theme.palette.mode === 'dark' ? '#2a2b2f' : '#e0e2e3',
    )}' stroke-width='2.2' stroke-linecap='round'/%3E%3C/svg%3E")`,
    borderRadius: '30px',
  },
  listItemContainer: {
    position: 'relative',
    paddingLeft: '32px',
  },
}))

const HOME_FEED_SLUG = 'community'

const SpaceItemButton = (props) => {
  const classes = useStyles()
  const { newGradient } = useAppTheme()
  const { isLastItem } = props
  const membership = useAppSelector((state) => selectMembership(state))

  const lineConnectorClass = isLastItem ? classes.lastLineConnector : classes.lineConnector
  return (
    <Box className={clsx('space-list-item-button mb-1', classes.listItemContainer)}>
      <div className={lineConnectorClass} />
      <ListItemButton
        className={clsx(props.inputs, 'border-color07')}
        sx={{
          height: '36px !important',
          background: !props.smDown && props.selected ? newGradient : undefined,
          '&.Mui-selected:hover': {
            background: !props.smDown && props.selected ? newGradient : undefined,
          },
          '& svg': {
            flexShrink: 0,
          },
        }}
        selected={props.selected}
        dense
        href={`/${membership.slug}/space/${props.item.slug}`}
        onClick={props.onClick}
        data-cy="space-menu-item"
        data-space={props.item.id}
      >
        {props.item.slug === HOME_FEED_SLUG ? <SVGHome /> : <SVGHashtag />}
        &nbsp;&nbsp;
        <ListItemText
          className="text-ellipsis"
          disableTypography={true}
          primary={
            <Typography
              sx={{
                fontFamily: 'Graphik SemiBold',
                fontWeight: 500,
                fontSize: '13px',
                textOverflow: 'ellipsis',
                overflow: 'hidden',
              }}
              title={props.item.name}
            >
              {props.item.name}
            </Typography>
          }
        />
      </ListItemButton>
    </Box>
  )
}

SpaceItemButton.propTypes = {
  inputs: PropTypes.any,
  smDown: PropTypes.any,
  selected: PropTypes.any,
  item: PropTypes.any,
  onClick: PropTypes.func,
}
const SpaceList = ({ isAdminOrCreator, isMobile, router, menuItemClass, onListItemClick }) => {
  const dispatch = useAppDispatch()

  const spaces = useSelector((state) => Object.values(state.feedAggregation.spaces))
  const theme = useTheme()
  const membership = useAppSelector((state) => selectMembership(state))
  const user = useAppSelector((state) => selectUser(state))
  let filteredSpaces = filterSpaces(spaces, membership, user)

  const { newGradient } = useAppTheme()

  const renderSpaces = () => {
    return (
      <>
        {filteredSpaces.length > 0 && (
          <>
            {/* Community */}
            <ListItemButton
              disableRipple={router.pathname === '/community'}
              className={clsx('community-menu-item', menuItemClass, 'border-color07', 'mb-1 mt-0')}
              sx={{
                height: '36px !important',
                marginBottom: '5px',
                background:
                  !isMobile && router && router.asPath === getCommunityBaseURL(membership) ? newGradient : undefined,
                '&.Mui-selected:hover': {
                  background:
                    !isMobile && router && router.asPath === getCommunityBaseURL(membership) ? newGradient : undefined,
                },
                position: 'relative',
                '&:hover .addButton': {
                  display: 'flex',
                },
              }}
              selected={router && router.asPath === getCommunityBaseURL(membership)}
              dense
              onClick={(e) => {
                e.preventDefault()
                e.stopPropagation()
                const pathname = `${getCommunityBaseURL(membership)}/`
                onListItemClick('Space', pathname)
                //dispatch(setActiveChannel(communitySpace))
              }}
              data-testid="space-menu-item-community"
            >
              {<SVGHome width={16} height={16} styles={{ position: 'relative', left: '2px' }} />}
              <ListItemText
                sx={{
                  marginLeft: '13px',
                  fontFamily: 'Graphik SemiBold',
                  fontSize: '14px',
                }}
                className="text-ellipsis"
                disableTypography={true}
                primary={
                  <Typography
                    sx={{
                      fontFamily: isMobile ? 'Graphik Semibold' : 'Graphik SemiBold',
                      fontSize: isMobile ? '20px !important' : '14px',
                      fontWeight: 500,
                      background: 'initial',
                      WebkitBackgroundClip: 'initial',
                      WebkitTextFillColor: 'initial',
                    }}
                  >
                    Community
                  </Typography>
                }
              />
              {isAdminOrCreator && (
                <ListItemButton
                  className="addButton"
                  sx={{
                    '&.MuiButtonBase-root': { borderRadius: '30px !important' },
                    backgroundColor: theme.palette.mode === 'dark' ? '#212124' : '#f5f5f5',
                    borderRadius: '30px',
                    padding: '0px',
                    width: '28px !important',
                    height: '28px !important',
                    '&.MuiListItemButton-root': { padding: '0px !important' },
                    position: 'absolute',
                    right: '5px',
                    display: isMobile ? 'flex' : 'none',
                    justifyContent: 'center',
                    alignItems: 'center',
                    '&:hover': {
                      backgroundColor: theme.palette.mode === 'dark' ? '#09090a' : '#dedcdc',
                    },
                  }}
                  dense
                  onClick={() => {
                    onListItemClick('Organize Spaces', '')
                  }}
                >
                  <ListItemIcon
                    sx={{
                      '&.MuiListItemIcon-root': {
                        marginRight: '0px !important',
                        width: '18px !important',
                        height: '17px !important',
                        lineHeight: '17px',
                        color: '#8D94A3',
                      },
                      '& svg': {
                        width: '17px',
                        height: '17px',
                      },
                    }}
                  >
                    <MoreHorizIcon fontSize="small" />
                  </ListItemIcon>
                </ListItemButton>
              )}
            </ListItemButton>

            {/* Spaces */}
            {!['/library', '/events', '/members'].includes(router.pathname) &&
              filteredSpaces.map((item, index) => {
                const isLastItem = index === filteredSpaces.length - 1
                const selected = router.asPath.endsWith(item.slug)

                console.log('SELECTED', router, item.slug)

                return (
                  <SpaceItemButton
                    key={item.id}
                    inputs={menuItemClass}
                    smDown={isMobile}
                    selected={selected}
                    isLastItem={isLastItem}
                    item={item}
                    onClick={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                      const pathname = `/${membership.slug}/space/${item.slug}`
                      onListItemClick('Space', pathname)
                      // FIX: 2091
                      ///dispatch(setActiveChannel(item))
                    }}
                  />
                )
              })}
          </>
        )}
      </>
    )
  }

  return (
    <List className="side-menu-space-list px-2 py-0" dense>
      {renderSpaces()}
    </List>
  )
}

SpaceList.propTypes = {
  isCurrentUserAdmin: PropTypes.bool.isRequired,
  isMobile: PropTypes.bool.isRequired,
  router: PropTypes.object.isRequired,
  menuItemClass: PropTypes.string,
  onListItemClick: PropTypes.func.isRequired,
}

export default SpaceList
