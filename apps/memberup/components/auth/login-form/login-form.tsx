import { zod<PERSON><PERSON>olver } from '@hookform/resolvers/zod'
import Image from 'next/image'
import Link from 'next/link'
import { useSearchParams } from 'next/navigation'
import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { LoginState, useLoginState } from './login-store'
import { authenticate } from '@/app/actions/auth/authenticate'
import { ShimmerButton } from '@/components/magicui/shimmer-button'
import { Button, ControlVariants, Input, PasswordInput } from '@/components/ui'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { passwordSchema } from '@/lib/validation/zod'
import { getActiveUserSuccess } from '@/memberup/store/features/userSlice'
import { useAppDispatch } from '@/memberup/store/hooks'
import memberupLogo from '@/public/assets/default/logos/memberup-logo.png'
import { getActiveUserApi } from '@/shared-services/apis/user.api'
import { IAuthenticatedUser } from '@/shared-types/interfaces'
import { AuthForms } from '@/store/authSlice'

interface LoginFormProps {
  mode: 'modal' | 'standalone'
  onSuccess: (data: IAuthenticatedUser) => void
  onForgotPasswordClick: () => void
}

export function LoginForm({ mode, onSuccess, onForgotPasswordClick }: LoginFormProps) {
  const dispatch = useAppDispatch()
  const searchParams = useSearchParams()
  const community = searchParams.get('community')
  const loginSchema = z.object({
    email: z.string().email(),
    password: passwordSchema,
  })
  const setShowForm = useStore((state) => state.auth.setShowForm)
  const setAuthenticatedUserData = useStore((state) => state.auth.setAuthenticatedUserData)
  const { loginState, setLoginState, isFormDisabled, navigateAndWait } = useLoginState()
  const [loginError, setLoginError] = useState(null)

  type LoginSchemaType = z.infer<typeof loginSchema>

  const onLoginSubmit = async (formData: LoginSchemaType) => {
    if (loginState !== LoginState.IDLE) return
    setLoginState(LoginState.AUTHENTICATING)
    setLoginError(null)

    try {
      const res = await authenticate({
        email: formData.email,
        password: formData.password,
        redirect: false,
      })

      if (!res.errorCode) {
        const userRes = await getActiveUserApi()
        dispatch(getActiveUserSuccess(userRes.data.data))
        setAuthenticatedUserData(userRes.data.data)

        const nextUrl = searchParams.get('next')
        if (nextUrl) {
          try {
            // Validate the next URL to ensure it's internal
            const url = new URL(nextUrl, window.location.origin)
            if (url.origin === window.location.origin) {
              await navigateAndWait(nextUrl)
              return
            }
          } catch {
            // Silently ignore invalid URLs
          }
        }

        // Call onSuccess only if no next URL was handled
        setLoginState(LoginState.SUCCESS)
        onSuccess(userRes.data.data)
      } else {
        if (res.errorCode === 'banned_user') {
          setLoginError(
            'You have been banned from MemberUp platform. Please contact support if you suspect this to be an error.',
          )
        } else if (res.errorCode === 'deleted_user') {
          setLoginError('Your account has been deleted. Please contact support if you suspect this to be an error.')
        } else {
          setLoginError('Invalid email or password.')
        }
        setLoginState(LoginState.ERROR)
      }
    } catch {
      toast.error(formSubmitError)
      setLoginState(LoginState.ERROR)
    }
  }

  const signupLink = community ? `/signup?community=${community}` : '/signup'

  const inputVariant = mode === 'standalone' ? ControlVariants.transparent : ControlVariants.default

  const handleKeyPress = (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key === 'Enter') {
      loginForm.handleSubmit(onLoginSubmit)()
    }
  }

  const handleInputOnClick = () => {
    if (loginState === LoginState.ERROR) {
      setLoginState(LoginState.IDLE)
      setLoginError(null)
    }
  }

  const loginForm = useForm<LoginSchemaType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      email: '',
      password: '',
    },
    resolver: zodResolver(loginSchema),
  })

  return (
    <div className="flex flex-col items-center">
      <Image className="mb-6" src={memberupLogo} width={170} height={25} alt="MemberUp" />
      <div className="mb-8 text-center text-2xl font-semibold leading-6 text-white-500">Log in to MemberUp</div>
      <Form {...loginForm}>
        <div className="w-full space-y-4">
          <FormField
            control={loginForm.control}
            name="email"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <Input
                    className="w-full"
                    autoComplete="email"
                    disabled={isFormDisabled}
                    type="email"
                    placeholder="Email"
                    error={Boolean(error)}
                    variant={inputVariant}
                    onKeyDown={handleKeyPress}
                    onClick={handleInputOnClick}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />
          <FormField
            control={loginForm.control}
            name="password"
            render={({ field, fieldState: { error } }) => (
              <FormItem>
                <FormControl>
                  <PasswordInput
                    className="w-full"
                    autoComplete="password"
                    placeholder="Password"
                    error={Boolean(error)}
                    variant={inputVariant}
                    disabled={isFormDisabled}
                    onKeyDown={handleKeyPress}
                    onClick={handleInputOnClick}
                    {...field}
                  />
                </FormControl>
                {error && <FormMessage>{error.message}</FormMessage>}
              </FormItem>
            )}
          />
        </div>
      </Form>
      <div className="w-full">
        <Button
          className="mt-2 inline-block text-sm font-normal text-primary-100 transition-colors hover:text-primary-200"
          onClick={onForgotPasswordClick}
          variant="inline"
        >
          Forgot Password?
        </Button>
        {loginError && <div className="text-medium mt-3 text-sm text-red-200">{loginError}</div>}
        <div className="my-5">
          <ShimmerButton
            className="w-full"
            type="submit"
            disabled={isFormDisabled}
            onClick={loginForm.handleSubmit(onLoginSubmit)}
            data-cy="signin-button"
            loading={isFormDisabled}
          >
            Log In
          </ShimmerButton>
        </div>
        <div className="text-center text-sm">
          <span className="text-black-100">Don&apos;t have an account? </span>
          {mode === 'standalone' ? (
            <Link className="text-primary-100 underline transition-colors hover:text-primary-200" href={signupLink}>
              Sign Up
            </Link>
          ) : (
            <Button
              className="text-primary-100 underline transition-colors hover:text-primary-200"
              onClick={(e) => {
                e.preventDefault()
                setShowForm(AuthForms.signup)
              }}
              variant="inline"
            >
              Sign Up
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
