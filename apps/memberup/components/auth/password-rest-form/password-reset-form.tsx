'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import Image from 'next/image'
import { ReactNode, useState } from 'react'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { showToast } from '@memberup/shared/src/libs/toast'
import { resetPasswordSendEmailApi } from '@memberup/shared/src/services/apis/reset-password.api'
import { Button, Input } from '@/components/ui'
import { Form, FormControl, FormField, FormItem, FormMessage } from '@/components/ui/form'
import { unexpectedError } from '@/lib/error-messages'
import memberupLogo from '@/public/assets/default/logos/memberup-logo.png'

interface FormHelperTextCustomProps {
  children: ReactNode
}

interface PasswordResetFormProps {
  backToLogin: () => void
}

const FormHelperTextCustom = ({ children }: FormHelperTextCustomProps) => {
  return <div className="pt-2 font-['Graphik'] text-[13px] font-normal text-red-500">{children}</div>
}

export const PasswordResetForm = ({ backToLogin }: PasswordResetFormProps) => {
  const resetPasswordSchema = z.object({
    email: z.string().email(),
  })

  type ResetPasswordSchemaType = z.infer<typeof resetPasswordSchema>

  const resetPasswordForm = useForm<ResetPasswordSchemaType>({
    mode: 'onSubmit',
    reValidateMode: 'onChange',
    defaultValues: {
      email: '',
    },
    resolver: zodResolver(resetPasswordSchema),
  })

  const [emailSent, setEmailSent] = useState('')
  const [requestingPasswordReset, setRequestingPasswordReset] = useState(false)
  const [error, setError] = useState(null)
  const onPasswordResetSubmit = async (data) => {
    setRequestingPasswordReset(true)
    try {
      const res = await resetPasswordSendEmailApi(data.email)

      if (res.data.success) {
        setEmailSent(data.email)
      }
    } catch (err) {
      const errorMessage = err?.response?.data?.message || err?.message
      if (errorMessage) {
        setError(errorMessage)
      } else {
        showToast(unexpectedError, 'error')
      }
    } finally {
      setRequestingPasswordReset(false)
    }
  }

  return (
    <div className="flex flex-col items-center">
      <Image className="mb-5" src={memberupLogo} width={170} height={25} alt="MemberUp" />
      <div className="mb-2.5 text-center text-lg font-semibold text-black-700 dark:text-white-500">
        {emailSent ? 'Please check your email' : 'Reset your password'}
      </div>
      <p className="mb-6 text-sm font-normal text-black-200 dark:text-black-100">
        {emailSent
          ? 'We emailed you a link to reset your password.'
          : "Enter your email below and we'll send you a reset link."}
      </p>
      {!emailSent && (
        <>
          <Form {...resetPasswordForm}>
            <div className="flex w-full flex-col">
              <FormField
                control={resetPasswordForm.control}
                name="email"
                render={({ field, fieldState: { error } }) => (
                  <FormItem>
                    <FormControl>
                      <Input
                        className="w-full"
                        autoComplete="email"
                        disabled={requestingPasswordReset}
                        type="email"
                        placeholder="Email"
                        error={Boolean(error)}
                        {...field}
                      />
                    </FormControl>
                    {error && <FormMessage>{error.message}</FormMessage>}
                  </FormItem>
                )}
              />
              {Boolean(error) && <FormHelperTextCustom>{error}</FormHelperTextCustom>}
              <div className="my-6">
                <Button
                  className="w-full"
                  loading={requestingPasswordReset}
                  disabled={!resetPasswordForm.formState.isValid || requestingPasswordReset}
                  onClick={resetPasswordForm.handleSubmit(onPasswordResetSubmit)}
                >
                  Send Email
                </Button>
              </div>
            </div>
          </Form>
        </>
      )}
      <Button
        className={`w-full ${!emailSent ? 'text-primary-100' : ''}`}
        onClick={backToLogin}
        variant={!emailSent ? 'inline' : 'default'}
      >
        {!emailSent ? 'Return to login' : 'Back to login'}
      </Button>
    </div>
  )
}
