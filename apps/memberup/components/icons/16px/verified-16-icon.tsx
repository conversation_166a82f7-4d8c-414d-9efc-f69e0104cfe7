export function Verified16Icon(props: React.SVGProps<SVGSVGElement>) {
  return (
    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 6.573585,13.912628 c -0.38687,-0.5661 -1.07193,-0.8499 -1.7458,-0.7232 -1.19493,0.2248 -2.24204,-0.8223 -2.01727,-2.0172 0.12676,-0.6739 -0.157,-1.3589505 -0.72313,-1.7458205 -1.00388,-0.68601 -1.00388,-2.16684 0,-2.85285 0.56613,-0.38687 0.84989,-1.07193 0.72313,-1.7458 -0.22477,-1.19493 0.82234,-2.24204 2.01727,-2.01727 0.67387,0.12676 1.35893,-0.157 1.7458,-0.72313 0.68601,-1.00388 2.16684,-1.00388 2.85285,0 0.38687,0.56613 1.07194,0.84989 1.74584,0.72313 1.1949,-0.22477 2.242,0.82234 2.0172,2.01727 -0.1267,0.67387 0.157,1.35893 0.7232,1.7458 1.0038,0.68601 1.0038,2.16684 0,2.85285 -0.5662,0.38687 -0.8499,1.0719205 -0.7232,1.7458205 0.2248,1.1949 -0.8223,2.242 -2.0172,2.017199 -0.6739,-0.1267 -1.35897,0.1571 -1.74584,0.7232 -0.68601,1.0039 -2.16684,1.0039 -2.85285,10e-7 z"
        fill="currentColor"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M 9.94799,5.85386 C 10.2596,5.54068 10.7662,5.53945 11.0794,5.8511 c 0.289,0.28769 0.3123,0.74142 0.0691,1.05584 L 11.0821,6.98247 7.89766,10.1825 C 7.6263,10.4552 7.20407,10.4931 6.89077,10.2865 L 6.80821,10.2241 5.19267,8.83119 C 4.85804,8.54268 4.82065,8.03753 5.10915,7.7029 5.37547,7.39401 5.82638,7.33839 6.15737,7.55858 l 0.08007,0.06081 1.05121,0.90597 z"
        fill="#ffffff"
      />
    </svg>
  )
}
