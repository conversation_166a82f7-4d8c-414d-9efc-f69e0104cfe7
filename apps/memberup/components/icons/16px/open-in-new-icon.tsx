import { SVGProps } from 'react'

export function OpenInNewIcon(props: SVGProps<SVGSVGElement>) {
  return (
    <svg viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M5 2C5.55228 2 6 2.44772 6 3C6 3.51284 5.61396 3.93551 5.11662 3.99327L5 4C4.48716 4 4.06449 4.38604 4.00673 4.88338L4 5V11C4 11.5128 4.38604 11.9355 4.88338 11.9933L5 12H11C11.5128 12 11.9355 11.614 11.9933 11.1166L12 11C12 10.4477 12.4477 10 13 10C13.5523 10 14 10.4477 14 11C14 12.5977 12.7511 13.9037 11.1763 13.9949L11 14H5C3.40232 14 2.09634 12.7511 2.00509 11.1763L2 11V5C2 3.34315 3.34315 2 5 2ZM13 2L13.081 2.003L13.2007 2.02024L13.3121 2.04974L13.4232 2.09367L13.5207 2.146L13.6168 2.21279L13.7071 2.29289L13.8037 2.40469L13.8753 2.51594L13.9063 2.5769L13.9401 2.65835L13.9642 2.73401L13.9931 2.8819L14 3V7C14 7.55228 13.5523 8 13 8C12.4477 8 12 7.55228 12 7V5.414L9.70711 7.70711C9.34662 8.06759 8.77939 8.09532 8.3871 7.7903L8.29289 7.70711C7.93241 7.34662 7.90468 6.77939 8.2097 6.3871L8.29289 6.29289L10.584 4H9C8.44772 4 8 3.55228 8 3C8 2.44772 8.44772 2 9 2H13Z"
        fill="currentColor"
      />
    </svg>
  )
}
