import React from 'react'

export const Refresh20Icon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M8.29289 4.29289L10.2929 2.29289C10.6834 1.90237 11.3166 1.90237 11.7071 2.29289C12.0676 2.65338 12.0953 3.22061 11.7903 3.6129L11.7071 3.70711L11.295 4.11955C14.5423 4.72694 17 7.57646 17 11C17 14.866 13.866 18 10 18C6.13401 18 3 14.866 3 11C3 9.94933 3.23232 8.92963 3.67401 7.99975C3.91097 7.50088 4.50748 7.28856 5.00634 7.52552C5.50521 7.76248 5.71753 8.35899 5.48057 8.85785C5.16558 9.521 5 10.2478 5 11C5 13.7614 7.23858 16 10 16C12.7614 16 15 13.7614 15 11C15 8.84177 13.6326 7.00291 11.7168 6.30255C12.0976 6.69468 12.0942 7.32006 11.7071 7.70711C11.3466 8.06759 10.7794 8.09532 10.3871 7.7903L10.2929 7.70711L8.29289 5.70711C7.93241 5.34662 7.90468 4.77939 8.2097 4.3871L8.29289 4.29289L10.2929 2.29289L8.29289 4.29289Z"
        fill="currentColor"
      />
    </svg>
  )
}

export default Refresh20Icon
