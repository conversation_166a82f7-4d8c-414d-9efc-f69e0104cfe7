import React from 'react'

export const Inbox20Icon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 17.999995,5.937505 c 0,-1.34619 -1.0745,-2.4375 -2.4,-2.4375 H 4.400005 c -1.32548,0 -2.4,1.09131 -2.4,2.4375 v 8.12499 c 0,1.3462 1.07452,2.4375 2.4,2.4375 h 11.19999 c 1.3255,0 2.4,-1.0913 2.4,-2.4375 z m -13.59999,-0.4375 h 11.19999 l 0.0701,0.00682 c 0.1692,0.03323 0.3077,0.18454 0.3275,0.38191 l -5.6369,4.48476 -0.0766,0.0548 -0.0786,0.0407 c -0.1669,0.0661 -0.3873,0.04 -0.55586,-0.0919 l -5.64963,-4.41406 v -0.02553 l 0.00668,-0.08045 c 0.03502,-0.20772 0.20318,-0.35705 0.39332,-0.35705 z m -0.4,3.00107 v 5.56142 c 0,0.2209 0.14704,0.3948 0.32995,0.4307 l 0.07005,0.0068 h 11.19999 c 0.1902,0 0.3583,-0.1493 0.3933,-0.357 l 0.0067,-0.0805 v -5.62024 l -4.4272,3.52154 -0.1448,0.1045 c -0.8685,0.5787 -1.99286,0.5732 -2.85261,-0.0015 l -0.15797,-0.1144 z"
        fill="currentColor"
      />
    </svg>
  )
}

export default Inbox20Icon
