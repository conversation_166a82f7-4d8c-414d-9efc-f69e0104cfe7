import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type Theme24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function Theme24Icon({ gradient, ...props }: Theme24IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className="chevron-left"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 12.00005,20.40422 c 0,0 -10e-5,0 -10e-5,0 -4.64155,0 -8.40428,-3.7627 -8.40428,-8.4042 0,-4.6416 3.76273,-8.40432 8.40428,-8.40432 0,0 10e-5,0 10e-5,0 z m 0,1.5957 c 0,0 -10e-5,0 -10e-5,0 -5.52279,0 -9.9999,-4.4771 -9.9999,-9.9999 0,-5.52283 4.47711,-9.99994 9.9999,-9.99994 0,0 10e-5,0 10e-5,0 5.5228,0 9.9999,4.47711 9.9999,9.99994 0,5.1776 -3.935,9.4362 -8.9776,9.9482 -0.3361,0.0342 -0.6772,0.0517 -1.0223,0.0517 z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <linearGradient
          id="community-gradient"
          x1="2.00005"
          y1="12"
          x2="21.99995"
          y2="12"
          gradientUnits="userSpaceOnUse"
        >
          <CommunityGradientStops />
        </linearGradient>
      )}
    </svg>
  )
}
