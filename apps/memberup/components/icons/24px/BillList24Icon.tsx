import React from 'react'

import { useStore } from '@/hooks/useStore'

type BillList24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function BillList24Icon({ gradient, ...props }: BillList24IconProps) {
  const colors = useStore((state) => state.community.colors)

  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className="chevron-left"
      {...props}
    >
      <path
        d="m 8.1302396,6.9994503 c -0.37398,0 -0.67715,0.30317 -0.67715,0.67715 0,0.37398 0.30317,0.67714 0.67715,0.67714 h 0.38694 c 0.37398,0 0.67715,-0.30316 0.67715,-0.67714 0,-0.37398 -0.30317,-0.67715 -0.67715,-0.67715 z m 2.7085504,0 c -0.3739,0 -0.6771,0.30317 -0.6771,0.67715 0,0.37398 0.3032,0.67714 0.6771,0.67714 h 5.0303 c 0.374,0 0.6771,-0.30316 0.6771,-0.67714 0,-0.37398 -0.3031,-0.67715 -0.6771,-0.67715 z"
        fill={gradient ? 'url(#paint0_linear_8326_17736)' : 'currentColor'}
        stroke={gradient ? 'url(#paint1_linear_8326_17736)' : 'currentColor'}
        strokeWidth="0.193471"
        strokeLinecap="round"
      />
      <path
        d="m 8.1302396,11.05604 c -0.37398,0 -0.67715,0.3032 -0.67715,0.6772 0,0.374 0.30317,0.6771 0.67715,0.6771 h 0.38694 c 0.37398,0 0.67715,-0.3031 0.67715,-0.6771 0,-0.374 -0.30317,-0.6772 -0.67715,-0.6772 z m 2.7085504,0 c -0.3739,0 -0.6771,0.3032 -0.6771,0.6772 0,0.374 0.3032,0.6771 0.6771,0.6771 h 5.0303 c 0.374,0 0.6771,-0.3031 0.6771,-0.6771 0,-0.374 -0.3031,-0.6772 -0.6771,-0.6772 z"
        fill={gradient ? 'url(#paint2_linear_8326_17736)' : 'currentColor'}
        stroke={gradient ? 'url(#paint3_linear_8326_17736)' : 'currentColor'}
        strokeWidth="0.193471"
        strokeLinecap="round"
      />
      <path
        d="m 8.1302396,15.11374 c -0.37398,0 -0.67715,0.3031 -0.67715,0.6771 0,0.374 0.30317,0.6772 0.67715,0.6772 h 0.38694 c 0.37398,0 0.67715,-0.3032 0.67715,-0.6772 0,-0.374 -0.30317,-0.6771 -0.67715,-0.6771 z m 2.7085504,0 c -0.3739,0 -0.6771,0.3031 -0.6771,0.6771 0,0.374 0.3032,0.6772 0.6771,0.6772 h 5.0303 c 0.374,0 0.6771,-0.3032 0.6771,-0.6772 0,-0.374 -0.3031,-0.6771 -0.6771,-0.6771 z"
        fill={gradient ? 'url(#paint4_linear_8326_17736)' : 'currentColor'}
        stroke={gradient ? 'url(#paint5_linear_8326_17736)' : 'currentColor'}
        strokeWidth="0.193471"
        strokeLinecap="round"
      />
      <rect
        x="3.8154497"
        y="1.8120003"
        width="16.369101"
        height="20.375999"
        rx="2.8961401"
        strokeWidth="1.93076"
        stroke={gradient ? 'url(#paint6_linear_8326_17736)' : 'currentColor'}
        fill="transparent"
      />
      {gradient && (
        <defs>
          <linearGradient
            id="paint0_linear_8326_17736"
            x1="7.4609399"
            y1="8.0452604"
            x2="16.3606"
            y2="8.0452604"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(0.0888896,-0.36865973)"
          >
            <stop stopColor={colors.secondary} />
            <stop offset="1" stopColor={colors.primary} />
          </linearGradient>
          <linearGradient
            id="paint1_linear_8326_17736"
            x1="7.4609399"
            y1="8.0452604"
            x2="16.3606"
            y2="8.0452604"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(0.0888896,-0.36865973)"
          >
            <stop stopColor={colors.secondary} />
            <stop offset="1" stopColor={colors.primary} />
          </linearGradient>
          <linearGradient
            id="paint2_linear_8326_17736"
            x1="7.4609399"
            y1="12.1019"
            x2="16.3606"
            y2="12.1019"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(0.0888896,-0.36865973)"
          >
            <stop stopColor={colors.secondary} />
            <stop offset="1" stopColor={colors.primary} />
          </linearGradient>
          <linearGradient
            id="paint3_linear_8326_17736"
            x1="7.4609399"
            y1="12.1019"
            x2="16.3606"
            y2="12.1019"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(0.0888896,-0.36865973)"
          >
            <stop stopColor={colors.secondary} />
            <stop offset="1" stopColor={colors.primary} />
          </linearGradient>
          <linearGradient
            id="paint4_linear_8326_17736"
            x1="7.4609399"
            y1="16.1595"
            x2="16.3606"
            y2="16.1595"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(0.0888896,-0.36865973)"
          >
            <stop stopColor={colors.secondary} />
            <stop offset="1" stopColor={colors.primary} />
          </linearGradient>
          <linearGradient
            id="paint5_linear_8326_17736"
            x1="7.4609399"
            y1="16.1595"
            x2="16.3606"
            y2="16.1595"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(0.0888896,-0.36865973)"
          >
            <stop stopColor={colors.secondary} />
            <stop offset="1" stopColor={colors.primary} />
          </linearGradient>
          <linearGradient
            id="paint6_linear_8326_17736"
            x1="3.7265601"
            y1="12.3687"
            x2="20.095699"
            y2="12.3687"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(0.0888896,-0.36865973)"
          >
            <stop stopColor={colors.secondary} />
            <stop offset="1" stopColor={colors.primary} />
          </linearGradient>
        </defs>
      )}
    </svg>
  )
}
