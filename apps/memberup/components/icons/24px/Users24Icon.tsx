import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type Users24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function Users24Icon({ gradient, ...props }: Users24IconProps) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 9.00001,12.00002 c 1.64868,0 3.15618,0.6155 4.31288,1.6331 0.7042,-0.279 1.4769,-0.4331 2.2871,-0.4331 3.3137,0 6,2.5788 6,5.76 0,0.7953 -0.6716,1.44 -1.5,1.44 H 4.05001 c -0.91127,0 -1.65,-0.7522 -1.65,-1.68 0,-3.7114 2.95492,-6.72 6.6,-6.72 z m -0.6,-8.40004 c 1.98818,0 3.59998,1.61178 3.59998,3.6 0,1.98823 -1.6118,3.60004 -3.59998,3.60004 -1.98823,0 -3.6,-1.61181 -3.6,-3.60004 0,-1.98822 1.61177,-3.6 3.6,-3.6 z m 8.39998,2.4 c 1.3255,0 2.4,1.07452 2.4,2.4 0,1.32549 -1.0745,2.40004 -2.4,2.40004 -1.3255,0 -2.4,-1.07455 -2.4,-2.40004 0,-1.32548 1.0745,-2.4 2.4,-2.4 z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <defs>
          <linearGradient
            id="community-gradient"
            x1="1.99951"
            y1="12.2441"
            x2="17.9995"
            y2="15.7441"
            gradientUnits="userSpaceOnUse"
          >
            <CommunityGradientStops />
          </linearGradient>
        </defs>
      )}
    </svg>
  )
}
