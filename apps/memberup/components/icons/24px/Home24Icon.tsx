import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type Home24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function Home24Icon({ gradient, ...props }: Home24IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className="chevron-left"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 20.4646,9.0124875 -6,-5.63542 c -1.3854,-1.30126 -3.5438,-1.30126 -4.92921,0 l -6,5.63542 C 2.81092,9.6929775 2.4,10.642678 2.4,11.636578 v 6.362299 c 0,1.9882 1.61178,3.6 3.6,3.6 h 2.4 7.2 2.4 c 1.9882,0 3.6,-1.611799 3.6,-3.6 v -6.3623 c 0,-0.9939 -0.4109,-1.9435995 -1.1354,-2.6240895 z M 15.6,19.198877 H 18 c 0.6154,0 1.1226,-0.4632 1.1919,-1.06 l 0.0081,-0.14 v -6.3623 c 0,-0.284 -0.1006,-0.5572 -0.2815,-0.7722 l -0.097,-0.1025 -6,-5.6354395 c -0.4233,-0.39761 -1.0631,-0.43074 -1.5225,-0.0994 l -0.1205,0.0994 -6.00004,5.6354395 c -0.20699,0.1944 -0.33719,0.454701 -0.37017,0.7338 L 4.8,11.636578 v 6.362299 c 0,0.6154 0.46325,1.1226 1.06006,1.1919 L 6,19.198877 h 2.4 v -6 c 0,-0.6627 0.53726,-1.2 1.2,-1.2 h 4.8 c 0.6627,0 1.2,0.5373 1.2,1.2 z m -4.8,0 h 2.4 v -4.8 h -2.4 z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <defs>
          <linearGradient
            id="community-gradient"
            x1="2.4003899"
            y1="12.7571"
            x2="21.600401"
            y2="12.7571"
            gradientUnits="userSpaceOnUse"
            gradientTransform="translate(-3.95e-4,-0.75710225)"
          >
            <CommunityGradientStops />
          </linearGradient>
        </defs>
      )}
    </svg>
  )
}
