import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type Info24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function Info24Icon({ gradient, ...props }: Info24IconProps) {
  return (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className="chevron-left fill-current"
      {...props}
    >
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 21.75001,12 c 0,5.3848 -4.3652,9.75 -9.75,9.75 -5.38479,0 -9.75002,-4.3652 -9.75002,-9.75 0,-5.38478 4.36523,-9.75 9.75002,-9.75 5.3848,0 9.75,4.36522 9.75,9.75 z m -8.5117,-3.72754 c 0,0.68335 -0.554,1.23731 -1.2373,1.23731 -0.6834,0 -1.2373,-0.55396 -1.2373,-1.23731 0,-0.68334 0.5539,-1.2373 1.2373,-1.2373 0.6833,0 1.2373,0.55396 1.2373,1.2373 z m -1.2416,2.20114 c -0.6821,0 -1.235,0.553 -1.235,1.235 v 4.0212 c 0,0.6821 0.5529,1.235 1.235,1.235 0.6821,0 1.235,-0.5529 1.235,-1.235 v -4.0212 c 0,-0.682 -0.5529,-1.235 -1.235,-1.235 z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <defs>
          <linearGradient
            id="community-gradient"
            x1="2.24999"
            y1="12"
            x2="21.75001"
            y2="12"
            gradientUnits="userSpaceOnUse"
          >
            <CommunityGradientStops />
          </linearGradient>
        </defs>
      )}
    </svg>
  )
}
