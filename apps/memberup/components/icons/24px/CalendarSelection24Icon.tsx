import React from 'react'

import { CommunityGradientStops } from '@/components/layout/community-gradient-stops'

type CalendarSelection24IconProps = React.SVGProps<SVGSVGElement> & {
  gradient?: boolean
}

export function CalendarSelection24Icon({ gradient, ...props }: CalendarSelection24IconProps) {
  return (
    <svg width="24" height="24" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" {...props}>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="m 16.79999,2.40002 c 0.6628,0 1.2,0.53726 1.2,1.2 v 1.2 c 1.9882,0 3.6,1.61177 3.6,3.6 v 9.59996 c 0,1.9883 -1.6118,3.6 -3.6,3.6 H 6.00001 c -1.98823,0 -3.6,-1.6117 -3.6,-3.6 V 8.40002 c 0,-1.98823 1.61177,-3.6 3.6,-3.6 v -1.2 c 0,-0.66274 0.53726,-1.2 1.2,-1.2 0.66274,0 1.2,0.53726 1.2,1.2 v 1.2 h 7.19998 v -1.2 c 0,-0.66274 0.5373,-1.2 1.2,-1.2 z m 2.4,9.59996 H 4.80001 v 6 c 0,0.6628 0.53726,1.2 1.2,1.2 h 11.99998 c 0.6628,0 1.2,-0.5372 1.2,-1.2 z m -2.4,1.2 c 0.6628,0 1.2,0.5373 1.2,1.2 v 2.4 c 0,0.6628 -0.5372,1.2 -1.2,1.2 h -2.4 c -0.6627,0 -1.2,-0.5372 -1.2,-1.2 v -2.4 c 0,-0.6627 0.5373,-1.2 1.2,-1.2 z m 1.2,-5.99996 H 6.00001 c -0.66274,0 -1.2,0.53726 -1.2,1.2 v 1.2 h 14.39998 v -1.2 c 0,-0.66274 -0.5372,-1.2 -1.2,-1.2 z"
        fill={gradient ? 'url(#community-gradient)' : 'currentColor'}
      />
      {gradient && (
        <defs>
          <linearGradient
            id="community-gradient"
            x1="2.40001"
            y1="12"
            x2="21.59999"
            y2="12"
            gradientUnits="userSpaceOnUse"
          >
            <CommunityGradientStops />
          </linearGradient>
        </defs>
      )}
    </svg>
  )
}
