import Image from 'next/image'
import { useEffect, useState } from 'react'
import { useDropzone } from 'react-dropzone'
import { v4 as uuidv4 } from 'uuid'

import { EditableGalleryMediaItem } from './editable-gallery'
import { PhotoAdd24Icon } from '@/components/icons'
import { ImageCropperDialog } from '@/components/images/image-cropper-dialog'
import { toast } from '@/components/ui/sonner'
import { getVideoScreenshot } from '@/lib/client/get-video-screenshot'
import { validateImage } from '@/lib/client/images'
import { validateVideo } from '@/lib/client/videos'
import { CropArea } from '@/shared-types/types'

const minImageWidth = 1400
const minImageHeight = 800
const maxImageSizeMB = 5
const minVideoWidth = 1280
const minVideoHeight = 720
const maxVideoSizeMB = 300

const getType = (file: File) => (file.type.indexOf('image') >= 0 ? 'image' : 'video')

export function EditableGalleryUploader({
  addMedia,
  media,
  saving,
}: {
  addMedia: (media: EditableGalleryMediaItem[]) => void
  media: any[]
  saving: boolean
}) {
  const [fileQueue, setFileQueue] = useState<any[]>([])

  const maxFiles = Math.max(0, 8 - (media?.length || 0))
  const croppingImage = fileQueue.find((item) => item.status === 'pending' && item.type === 'image')

  const processQueue = async (queue?: any[]) => {
    const queueToProcess = queue || fileQueue

    const newMedia: any[] = []

    for (const item of queueToProcess) {
      if (['error'].includes(item.status)) {
        const url = item.type === 'image' ? item.url : await getVideoScreenshot(item.file)

        toast.error(
          <div className="flex">
            <Image className="mr-4 block max-h-10 max-w-10" alt="" src={url} width={100} height={100} />
            <div>{item.error}</div>
          </div>,
        )
      } else {
        newMedia.push(item)
      }
    }

    addMedia(newMedia)
  }

  const handleDropFiles = async (acceptedFiles: File[]) => {
    let hasValidImageFiles = false

    const queue: any[] = []

    for (const file of acceptedFiles) {
      const mediaType = getType(file)

      if (mediaType === 'image') {
        const validationResult = await validateImage(file, minImageWidth, minImageHeight, maxImageSizeMB)

        if (!validationResult.valid) {
          queue.push({
            id: uuidv4(),
            type: mediaType,
            file: file,
            status: 'error',
            error: validationResult.error,
            url: URL.createObjectURL(file),
          })
        } else {
          hasValidImageFiles = true
          queue.push({ id: uuidv4(), type: mediaType, file: file, status: 'pending', url: URL.createObjectURL(file) })
        }
      } else if (mediaType === 'video') {
        const validationResult = await validateVideo(file, minVideoWidth, minVideoHeight, maxVideoSizeMB)

        if (!validationResult.valid) {
          queue.push({ id: uuidv4(), type: mediaType, file: file, status: 'error', error: validationResult.error })
        } else {
          queue.push({ id: uuidv4(), type: mediaType, file: file, status: 'pending' })
        }
      }
    }

    if (hasValidImageFiles) {
      setFileQueue(queue)
    } else {
      await processQueue(queue)
    }
  }

  const { getRootProps, getInputProps, fileRejections, isDragActive } = useDropzone({
    onDrop: handleDropFiles,
    maxFiles,
    accept: {
      'image/*': [],
      'video/*': [],
    },
    disabled: saving,
  })

  const onCropComplete = async (cropArea: CropArea, croppedImageBlob: string) => {
    let hasImagesPendingCrop = false

    const updatedQueue = []

    setFileQueue((prev) => {
      const ret = prev.map((item) => {
        if (item.id === croppingImage.id) {
          const updatedItem = {
            ...item,
            cropArea,
            url: croppedImageBlob,
            status: 'valid',
          }
          updatedQueue.push(updatedItem)
          return updatedItem
        } else if (item.type === 'image' && item.status === 'pending') {
          hasImagesPendingCrop = true
        }

        updatedQueue.push(item)
        return item
      })

      return ret
    })

    if (!hasImagesPendingCrop) {
      processQueue(updatedQueue)
    }
  }

  useEffect(() => {
    if (fileRejections.length > 0) {
      if (fileRejections[0].errors[0].code === 'too-many-files') {
        let message = `You can only select ${maxFiles}`

        if (maxFiles === 1) {
          message += ' more image or video'
        } else {
          message += ' more images or videos'
        }

        toast.error(message)
      }
    }
  }, [fileRejections])

  return (
    <>
      <div
        {...getRootProps({
          className: 'w-full h-full cursor-pointer flex justify-center items-center',
        })}
      >
        <input {...getInputProps()} />
        {isDragActive ? (
          <p>Drop the files here ...</p>
        ) : (
          <div className="flex select-none flex-col items-center text-sm font-medium text-black-700 dark:text-white-500">
            <PhotoAdd24Icon className="mb-3 text-black-200" />
            <div>Upload Image or Video</div>
            <div className="mt-3 text-community-primary">
              <span className="hidden md:inline">Drag and drop or click to upload</span>
              <span className="inline md:hidden">Tap to upload file</span>
            </div>
          </div>
        )}
      </div>
      {croppingImage && (
        <ImageCropperDialog
          aspectRatio={16 / 9}
          url={croppingImage.url}
          minWidth={minImageWidth}
          minHeight={minImageHeight}
          file={croppingImage.file}
          open={true}
          onCropComplete={onCropComplete}
          onClose={() => setFileQueue([])}
          cropShape="rect"
          variant="community-primary"
        />
      )}
    </>
  )
}
