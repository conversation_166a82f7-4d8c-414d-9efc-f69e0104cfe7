'use client'

import { useEffect, useRef, useState } from 'react'

import { Separator, SkeletonBox } from '@/components/ui'
import { AvatarList } from '@/components/ui/avatar-list'
import { useStore } from '@/hooks/useStore'
import { formatThousands } from '@/lib/formatting'
import { type ICommunityStats } from '@/lib/types/communities'
import { getMembershipStatsApi } from '@/shared-services/apis/membership.api'
import { IMembership } from '@/shared-types/interfaces'

export function CommunityStats() {
  const membership = useStore((state) => state.community.membership)
  const [stats, setStats] = useState<ICommunityStats | null>(null)
  const [loading, setLoading] = useState(true)
  const updateStatsIntervalRef = useRef(null)
  const membershipRef = useRef<IMembership>(null)

  useEffect(() => {
    if (!membership || membership === membershipRef.current) {
      return
    }

    membershipRef.current = membership

    const fetchStats = async () => {
      const res = await getMembershipStatsApi(membership.id)
      setStats(res.data)
      setLoading(false)
    }

    fetchStats()

    updateStatsIntervalRef.current = setInterval(() => {
      fetchStats()
    }, 60000)

    return () => {
      if (updateStatsIntervalRef.current) {
        clearInterval(updateStatsIntervalRef.current)
        updateStatsIntervalRef.current = null
      }
    }
  }, [membership])

  return (
    <>
      <div className="flex justify-center space-x-10 py-2.5">
        {loading ? (
          <>
            <SkeletonBox className="h-10 w-10" />
            <SkeletonBox className="h-10 w-10" />
            <SkeletonBox className="h-10 w-10" />
          </>
        ) : (
          <>
            <div className="text-center">
              <div className="text-base font-semibold">{formatThousands(stats?.totalMembers)}</div>
              <div className="text-xs text-black-200 dark:text-black-100">Members</div>
            </div>
            <div className="text-center">
              <div className="flex items-center justify-center text-base font-semibold">
                {formatThousands(stats?.totalOnline)} <span className="ml-1 h-2 w-2 rounded-full bg-green-200" />
              </div>
              <div className="text-xs text-black-200 dark:text-black-100">Online</div>
            </div>
            <div className="text-center">
              <div className="text-base font-semibold">{stats?.totalAdmins}</div>
              <div className="text-xs text-black-200 dark:text-black-100">Admins</div>
            </div>
          </>
        )}
      </div>
      <Separator />
      {!loading && stats?.recentActiveMembers?.length > 0 && (
        <div className="px-5 pt-5 text-center">
          <AvatarList users={stats?.recentActiveMembers} />
        </div>
      )}
    </>
  )
}
