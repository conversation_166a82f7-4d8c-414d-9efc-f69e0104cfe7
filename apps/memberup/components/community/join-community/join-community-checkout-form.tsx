import { CardElement, Elements, useElements, useStripe } from '@stripe/react-stripe-js'
import { loadStripe } from '@stripe/stripe-js'
import { useRouter, useSearchParams } from 'next/navigation'
import React, { useEffect, useMemo, useRef, useState } from 'react'

import { Favicon } from '../favicon'
import { Button, SkeletonBox } from '@/components/ui'
import { toast } from '@/components/ui/sonner'
import { useStore } from '@/hooks/useStore'
import { getStripeSecureReturnUrl } from '@/lib/stripe'
import { cn } from '@/lib/utils'
import { useAppSelector } from '@/memberup/store/store'
import { STRIPE_PUBLISH_KEY } from '@/shared-config/envs'
import { getUserMembershipApi, joinMembershipApi } from '@/shared-services/apis/membership.api'
import {
  createStripeInvoiceAndPayApi,
  createStripeMembershipSetupIntentApi,
  createStripeMembershipSubscriptionApi,
} from '@/shared-services/apis/stripe.api'
import { extractPriceOrDefault, formatPrice, getCommunityBaseURL } from '@/src/libs/utils'
import { selectMembershipSetting } from '@/src/store/features/membershipSlice'

const PAYMENT_CHECK_POLL_INTERVAL = 5000

function JoinCommunityCheckoutFormContents({
  clientSecret,
  onSuccess,
  stripePromise,
  selectedPrice,
}: {
  clientSecret?: string
  onSuccess: any
  stripePromise: any
  selectedPrice: any
}) {
  const stripe = useStripe()
  const elements = useElements()
  const [cardComplete, setCardComplete] = useState(false)
  const [cardInputEmpty, setCardInputEmpty] = useState(true)
  const [cardError, setCardError] = useState(false)
  const [error, setError] = useState('')
  const [isPaymentProcessing, setIsPaymentProcessing] = useState(false)
  const membership = useStore((state) => state.community.membership)
  const intervalRef = useRef(null)
  const router = useRouter()
  const setJoinCommunityId = useStore((state) => state.auth.setJoinCommunityId)
  const updateUserMembership = useStore((state) => state.auth.updateUserMembership)

  useEffect(() => {
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
    }
  }, [])

  const handleFormSubmit = async (e: React.FormEvent) => {
    try {
      e.preventDefault()
      setIsPaymentProcessing(true)

      // Confirm card setup
      const cardElement = elements.getElement(CardElement)
      const { error, setupIntent } = await stripe.confirmCardSetup(clientSecret, {
        payment_method: {
          card: cardElement,
        },
        return_url: getStripeSecureReturnUrl(),
      })

      if (error) {
        throw new Error(error.message)
      }

      const isRecurring = Boolean(selectedPrice.recurring?.interval)
      if (isRecurring) {
        const result = await createStripeMembershipSubscriptionApi(
          {
            collection_method: 'charge_automatically',
            default_payment_method: setupIntent.payment_method as string,
            selected_price_id: selectedPrice.id,
          },
          membership.id,
        )
        const data = result.data?.data
        if (data.status === 'requires_action') {
          const stripe = await stripePromise
          const subscription = data.stripe_subscription
          await stripe.confirmCardPayment(subscription.latest_invoice.payment_intent.client_secret)
        }
      } else {
        const result = await createStripeInvoiceAndPayApi(
          {
            selected_price_id: selectedPrice.id,
            lifetime_mode: true,
            payment_method: setupIntent.payment_method as string,
          },
          membership.id,
        )
      }

      // Monitor payment
      if (intervalRef.current) {
        clearInterval(intervalRef.current)
      }
      intervalRef.current = setInterval(() => {
        getUserMembershipApi(membership.id).then((res) => {
          const status = res.data.data.status
          if (status === 'accepted') {
            clearInterval(intervalRef.current)
            updateUserMembership(res.data.data)
            toast.success('Payment successful. You have joined the community.')
            router.push(getCommunityBaseURL(membership))
            setIsPaymentProcessing(false)
            setJoinCommunityId(null)
            onSuccess?.()
          }
        })
      }, PAYMENT_CHECK_POLL_INTERVAL)
    } catch (e) {
      toast.error(e.message)
    } finally {
      setIsPaymentProcessing(false)
    }
  }

  return (
    <form autoComplete="off" onSubmit={handleFormSubmit}>
      <div>
        <h3 className="mb-3 text-sm font-semibold text-black-700 dark:text-white-500">Payment information</h3>
        <CardElement
          className={cn(
            'rounded-base border border-black-700 bg-black-100/[0.08] px-4 text-black-100 [&_iframe]:focus:outline-none',
            cardError ? 'border-red-200' : cardInputEmpty ? 'border-black-100/[0.08]' : 'border-black-100',
          )}
          options={{
            hidePostalCode: false,
            style: {
              base: {
                color: 'white',
                lineHeight: '3.4285rem',
                '::placeholder': {
                  color: '#8D94A3',
                },
              },
              invalid: {
                color: '#F34646',
                iconColor: '#F34646',
              },
            },
          }}
          onChange={(e) => {
            if (e.empty !== cardInputEmpty) {
              setCardInputEmpty(e.empty)
            }
            if (e.complete !== cardComplete) {
              setCardComplete(e.complete)
            }
            if (Boolean(e.error) !== cardError) {
              setCardError(Boolean(e.error))
            }
          }}
        />
        {Boolean(error) && <div>{error}</div>}
      </div>
      <Button
        className="rounded-2x mt-8 w-full bg-violet-700 p-2 font-bold"
        type="submit"
        disabled={isPaymentProcessing || !cardComplete}
        data-cy="join-community-checkout-button"
        loading={isPaymentProcessing}
      >
        Join
      </Button>
    </form>
  )
}

export function JoinCommunityCheckoutForm({
  onSuccess,
  onError,
}: {
  onSuccess?: (newMembership: any) => void
  onError?: (e: string) => void
}) {
  const [subscriptionIntentClientSecret, setSubscriptionIntentClientSecret] = useState<string>(null)
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = useAppSelector((state) => selectMembershipSetting(state))
  const joinCommunityId = useStore((state) => state.auth.joinCommunityId)
  const userMembership = useStore((state) => state.community.userMembership)
  const creatingPaymentIntentRef = useRef(false)
  const generatingUserMembershipRef = useRef(false)
  const updateUserMembership = useStore((state) => state.auth.updateUserMembership)

  useEffect(() => {
    const generateUserMembership = async () => {
      const response = await joinMembershipApi(joinCommunityId)
      if (response.status !== 200) {
        throw new Error('Failed to generate user membership.')
      }
      const data = response.data.data
      updateUserMembership(data)
    }

    // Always regenerate user membership when joining to ensure status is updated
    if (joinCommunityId && !generatingUserMembershipRef.current) {
      generatingUserMembershipRef.current = true
      generateUserMembership()
    }
  }, [joinCommunityId])

  const stripePromise = useMemo(() => {
    if (!membershipSetting?.stripe_connect_account) return

    return loadStripe(STRIPE_PUBLISH_KEY, {
      stripeAccount: membershipSetting?.stripe_connect_account.stripe_user_id,
    })
  }, [membershipSetting?.stripe_connect_account])

  const startMembershipSetupIntent = async () => {
    if (creatingPaymentIntentRef.current) {
      return
    }

    try {
      const res = await createStripeMembershipSetupIntentApi(membership.id)
      setSubscriptionIntentClientSecret(res.data.data['client_secret'])
    } catch (error) {
      console.error(error)
    }
  }

  useEffect(() => {
    if (!userMembership || !membershipSetting) {
      return
    }

    if (userMembership.user_role !== 'accepted') {
      startMembershipSetupIntent()
    }
  }, [membership, userMembership])

  const searchParams = useSearchParams()
  const promoCode = searchParams.get('promo_code')
  const selectedPrice = extractPriceOrDefault(membershipSetting.stripe_prices, promoCode)
  const formattedPrice = formatPrice(selectedPrice)

  return (
    <div className="flex flex-col justify-center p-2">
      <div className="mb-5 flex w-full justify-center">
        <Favicon
          className="mr-4 h-16 w-16"
          communityName={membership.name}
          src={membershipSetting.favicon}
          cropArea={membershipSetting.favicon_crop_area}
          width={48}
          height={48}
          variant="inverted"
        />
      </div>
      <div className="mb-5 text-center text-2xl font-semibold text-black-700 dark:text-white-500">
        {membership?.name}
      </div>
      <div className="mb-10 text-center text-sm font-normal leading-snug text-black-200 dark:text-black-100">
        {formattedPrice} membership. Cancel anytime.
      </div>
      <div className="relative mb-10 w-full overflow-visible">
        <div className="absolute -left-10 h-px w-[calc(100%+5rem)] bg-grey-900" />
      </div>
      <div>
        {userMembership && Boolean(stripePromise) && Boolean(subscriptionIntentClientSecret) ? (
          <Elements
            stripe={stripePromise}
            options={{
              clientSecret: subscriptionIntentClientSecret,
              appearance: {
                theme: 'night',
              },
              loader: 'always',
            }}
          >
            <JoinCommunityCheckoutFormContents
              stripePromise={stripePromise}
              clientSecret={subscriptionIntentClientSecret}
              onSuccess={onSuccess}
              selectedPrice={selectedPrice}
            />
          </Elements>
        ) : (
          <SkeletonBox className="h-28 w-full" variant="dialog" />
        )}
      </div>
      <div className="mt-5 text-center text-sm text-black-200 dark:text-black-100">
        By joining, you accept {membership.name} and MemberUp’s&nbsp;
        <a
          className="hover:text-100 text-black-200 underline transition-colors dark:text-black-100 dark:hover:text-black-200"
          href="https://memberup.com/privacy-policy"
          target="_blank"
          rel="noreferrer"
        >
          terms
        </a>
        . You can cancel anytime.
      </div>
    </div>
  )
}
