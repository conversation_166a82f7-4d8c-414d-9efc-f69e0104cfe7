import { MembershipSettings } from './membership-settings'
import { NotificationsSettings } from './notification-settings'
import { Favicon } from '@/components/community/favicon'
import { InviteSettings } from '@/components/settings/member-settings/invite-settings'
import { SettingsModal, SettingsModalConfig } from '@/components/ui/settings-modal'
import { useStore } from '@/hooks/useStore'

/*
This opens from the community info as a member of the community.
 */
export function MemberSettings({ open, onOpenChange }: { open: boolean; onOpenChange: (value: boolean) => void }) {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting

  if (!membership) {
    return
  }

  const config: SettingsModalConfig = {
    sections: [
      {
        name: 'membership',
        title: 'Membership',
        component: MembershipSettings,
      },
      {
        name: 'notifications',
        title: 'Notifications',
        component: NotificationsSettings,
      },
      {
        name: 'invite',
        title: 'Invite',
        component: InviteSettings,
      },
    ],
  }

  if (!membership || !membershipSetting) {
    return
  }

  return (
    <SettingsModal
      header={
        <>
          <Favicon
            className="mr-3 h-12 w-12"
            communityName={membership.name}
            src={membershipSetting.favicon}
            cropArea={membershipSetting.favicon_crop_area}
            width={48}
            height={48}
          />
          <div className="select-none">
            <h1 className="text-base font-semibold text-black-700 dark:text-white-500">{membership.name}</h1>
            <p className="text-ssm text-black-200 dark:text-black-100">Membership settings</p>
          </div>
        </>
      }
      open={open}
      onOpenChange={onOpenChange}
      config={config}
    />
  )
}
