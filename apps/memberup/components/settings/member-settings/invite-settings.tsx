import { ClipboardCopyInput } from '@/components/ui/clipboard-copy-input'
import { useStore } from '@/hooks/useStore'

export function InviteSettings() {
  const membership = useStore((state) => state.community.membership)
  const communityInviteLink = `https://www.memberup.com/${membership.slug}/about`

  return (
    <div>
      <h2 className="mb-0.5 text-lg font-semibold text-white-500">Invite</h2>
      <p className="mb-4 text-sm font-normal text-black-600 dark:text-white-200">
        Share a link to <span className={'font-semibold'}>{membership.name}</span> with your friends.
      </p>
      <ClipboardCopyInput value={communityInviteLink} />
    </div>
  )
}
