import { useForm } from 'react-hook-form'

import { NOTIFICATION_SETTINGS } from '@memberup/shared/src/settings/notifications'
import { Button, Checkbox } from '@/components/ui'
import { Form, FormControl, FormField, FormItem } from '@/components/ui/form'
import { toast } from '@/components/ui/sonner'
import { useUpdateUserProfile } from '@/hooks/users/useUpdateUserProfile'
import { useStore } from '@/hooks/useStore'
import { formSubmitError } from '@/lib/error-messages'
import { cn } from '@/lib/utils'

export function NotificationsSettings({ inUserSettings }: { inUserSettings?: boolean }) {
  const profile = useStore((state) => state.auth.profile)
  const { saving, updateUserProfile } = useUpdateUserProfile()

  const defaultValues: Record<string, string> = NOTIFICATION_SETTINGS.reduce((acc, value) => {
    const emailValue = profile?.enable_notifications?.[value.name]?.['email']
    const inAppValue = profile?.enable_notifications?.[value.name]?.['in_app_feed']

    acc[`${value.name}`] = {
      email: emailValue !== undefined ? emailValue : value.email,
      in_app_feed: inAppValue !== undefined ? inAppValue : value.in_app_feed,
    }

    return acc
  }, {})

  const form = useForm({
    mode: 'onChange',
    reValidateMode: 'onChange',
    defaultValues,
  })

  const handleContinue = async (payload: any) => {
    const success = await updateUserProfile({
      enable_notifications: payload,
    })

    if (success) {
      toast.success('Your notification settings have been updated.')
    } else {
      toast.error(formSubmitError)
    }
  }

  return (
    <div className="tailwind-component">
      <Form {...form}>
        <form className="space-y-6" autoComplete="off">
          <div>
            <table className={cn('-mt-1 w-full', inUserSettings ? 'text-sm' : 'text-base')}>
              <thead>
                <tr>
                  <td className="w-11/12 pb-2">
                    {inUserSettings ? (
                      <h2 className="text-lg font-semibold">Profile settings</h2>
                    ) : (
                      <h2 className="font-semibold text-white-500">Notifications</h2>
                    )}
                  </td>
                  <td className="whitespace-nowrap text-nowrap pb-2 pr-10 dark:text-black-100">Email</td>
                  <td className="whitespace-nowrap text-nowrap pb-2 dark:text-black-100">In-app</td>
                </tr>
              </thead>
              <tbody>
                {NOTIFICATION_SETTINGS.map((notification) => (
                  <tr key={notification.id}>
                    <td className="pt-6 text-black-200 dark:text-black-100">{notification.title}</td>
                    <td className="pr-10 pt-6">
                      <FormField
                        control={form.control}
                        name={`${notification.name}.email`}
                        render={({ field }) => (
                          <FormItem className="flex justify-center">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                disabled={saving}
                                onCheckedChange={field.onChange}
                                variant="community-primary"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </td>
                    <td className="pt-6">
                      <FormField
                        control={form.control}
                        name={`${notification.name}.in_app_feed`}
                        render={({ field }) => (
                          <FormItem className="flex justify-center">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                disabled={saving}
                                onCheckedChange={field.onChange}
                                variant="community-primary"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </form>
      </Form>
      <div className="flex justify-end pt-10">
        <Button
          variant="community-primary"
          disabled={saving}
          onClick={(e) => {
            e.preventDefault()
            form.handleSubmit(handleContinue)()
          }}
          loading={saving}
        >
          Save
        </Button>
      </div>
    </div>
  )
}
