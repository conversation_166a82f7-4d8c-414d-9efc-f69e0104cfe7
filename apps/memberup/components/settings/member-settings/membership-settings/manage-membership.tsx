import Link from 'next/link'
import <PERSON><PERSON> from 'stripe'

import { MembershipSettingsSections } from './constants'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { ArrowLeft20Icon, CreditCard24Icon } from '@/components/icons'
import { OpenInNewIcon } from '@/components/icons/16px/open-in-new-icon'
import Inbox20Icon from '@/components/icons/20px/inbox-20-icon'
import { Button, Separator } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { formatCommunitySubscriptionAmountSuffix, formatPaymentMethod } from '@/lib/stripe'

interface ManageMembershipProps {
  onBack: () => void
  nextInvoice: Stripe.Invoice | null
  subscription: Stripe.Subscription | null
  setSection: (section: MembershipSettingsSections) => void
}

export function ManageMembership({ onBack, nextInvoice, subscription, setSection }: ManageMembershipProps) {
  const membership = useStore((state) => state.community.membership)

  const menuItemClass =
    'justify-start text-center text-sm font-semibold text-black-200 hover:underline dark:text-black-100'

  return (
    <div className="text-sm">
      <h2 className="mb-3 text-lg font-semibold text-black-700 dark:text-white-500">Manage Membership</h2>
      <p className="mb-6 text-sm leading-relaxed text-black-600 dark:text-white-500">
        {nextInvoice && (
          <>
            You&apos;re paying ${(nextInvoice.total / 100).toFixed(2)}
            {formatCommunitySubscriptionAmountSuffix(subscription)} for {membership.name}.<br />
            Next payment: {formatDate({ date: new Date(nextInvoice.period_end * 1000), format: 'MMM do, y' })}
            {subscription.default_payment_method && (
              <>- {formatPaymentMethod(subscription.default_payment_method as Stripe.PaymentMethod)}</>
            )}
          </>
        )}
      </p>
      <div className="flex flex-col space-y-4">
        {subscription && (
          <div className="w-full">
            <Link href={`/settings/account/payment-history?membershipId=${membership.id}`} className={menuItemClass}>
              View payment history <OpenInNewIcon className="ml-0.5 inline-block h-3 w-3" />
            </Link>
          </div>
        )}
        <Button
          variant="inline"
          className={menuItemClass}
          onClick={() => setSection(MembershipSettingsSections.CANCEL_MEMBERSHIP)}
        >
          {subscription ? 'Cancel membership' : 'Leave community'}
        </Button>
        <Separator />
        {subscription && (
          <div className="w-full">
            <Button
              variant="inline"
              className={menuItemClass}
              onClick={() => setSection(MembershipSettingsSections.UPDATE_PAYMENT_METHOD)}
            >
              <CreditCard24Icon className="mr-2 h-5 w-5" /> Update payment method
            </Button>
          </div>
        )}
        <Button
          variant="inline"
          className={menuItemClass}
          onClick={() => setSection(MembershipSettingsSections.CONTACT_SUPPORT)}
        >
          <Inbox20Icon className="mr-2" /> Contact support
        </Button>
        <Button className={menuItemClass} variant="inline" onClick={onBack}>
          <ArrowLeft20Icon className="mr-2 h-5 w-5" /> Back
        </Button>
      </div>
    </div>
  )
}
