import Link from 'next/link'

import { But<PERSON> } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { getCommunityBaseURL } from '@/src/libs/utils'

export function MembershipUpdatePaymentMethod({ onBack }: { onBack: () => void }) {
  const membership = useStore((state) => state.community.membership)

  return (
    <div>
      <h2 className="mb-3 text-lg font-semibold text-black-700 dark:text-white-500">Contact Support</h2>
      <Button
        className="inline-block text-sm font-medium leading-normal text-primary-100 hover:underline"
        onClick={onBack}
        variant="inline"
      >
        Back
      </Button>
      <p className="mb-6 text-sm text-black-600 dark:text-black-100">
        If you need help, you can DM the{' '}
        <Link className="text-primary" href={`${getCommunityBaseURL(membership)}/members`}>
          community admins
        </Link>
        {membership.membership_setting.support_email && `or email ${membership.membership_setting.support_email}`}
      </p>
    </div>
  )
}
