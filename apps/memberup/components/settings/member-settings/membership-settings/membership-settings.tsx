import Link from 'next/link'
import { useCallback, useEffect, useState } from 'react'

import { CancelMembership } from './cancel-membership'
import { MembershipSettingsSections } from './constants'
import { MembershipContactSupport } from './contact-support'
import { ManageMembership } from './manage-membership'
import { MembershipUpdatePaymentMethod } from './update-payment-method'
import { formatDate } from '@memberup/shared/src/libs/date-utils'
import { Settings24Icon } from '@/components/icons'
import { Inbox20Icon } from '@/components/icons/20px/inbox-20-icon'
import { ErrorMessageRetry } from '@/components/layout/error-message-retry'
import { Button, Separator, SkeletonBox } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { formatPaymentMethod } from '@/lib/stripe'
import { getStripeSubscriptionsApi, getStripeUpcomingInvoiceApi } from '@/shared-services/apis/stripe.api'

export function MembershipSettings() {
  const membership = useStore((state) => state.community.membership)
  const membershipSetting = membership?.membership_setting
  const [subscription, setSubscription] = useState(null)
  const [nextInvoice, setNextInvoice] = useState(null)
  const [loading, setLoading] = useState(true)
  const userMembership = useStore((state) => state.community.userMembership)
  const [section, setSection] = useState(MembershipSettingsSections.MAIN)
  const [error, setError] = useState(false)

  const fetchSubscriptions = useCallback(async () => {
    if (!membershipSetting.stripe_connect_account?.stripe_user_id) {
      setLoading(false)
      return
    }

    try {
      const [subscriptionResponse, upcomingInvoiceResponse] = await Promise.all([
        getStripeSubscriptionsApi(true, membership.id, true),
        getStripeUpcomingInvoiceApi(true, membership.id),
      ])

      const subscriptions = subscriptionResponse.data.data.data
      setSubscription(subscriptions.length > 0 ? subscriptions[0] : null)
      setNextInvoice(upcomingInvoiceResponse.data.data)
      setLoading(false)
    } catch (error) {
      console.error(error)
      setError(true)
      setLoading(false)
    }
  }, [membershipSetting.stripe_connect_account?.stripe_user_id, membership.id])

  useEffect(() => {
    if (!membershipSetting) {
      return
    }
    fetchSubscriptions()
  }, [membership.id, membershipSetting, fetchSubscriptions])

  const onRetry = () => {
    setError(false)
    fetchSubscriptions()
  }

  if (error) {
    return <ErrorMessageRetry onRetry={onRetry} />
  }

  if (section === MembershipSettingsSections.MANAGE_MEMBERSHIP) {
    return (
      <ManageMembership
        onBack={() => setSection(MembershipSettingsSections.MAIN)}
        nextInvoice={nextInvoice}
        setSection={setSection}
        subscription={subscription}
      />
    )
  }

  if (section === MembershipSettingsSections.CANCEL_MEMBERSHIP) {
    return <CancelMembership subscription={subscription} nextInvoice={nextInvoice} setSection={setSection} />
  }

  if (section === MembershipSettingsSections.CONTACT_SUPPORT) {
    return <MembershipContactSupport onBack={() => setSection(MembershipSettingsSections.MAIN)} />
  }

  if (section === MembershipSettingsSections.UPDATE_PAYMENT_METHOD) {
    return <MembershipUpdatePaymentMethod onBack={() => setSection(MembershipSettingsSections.MAIN)} />
  }

  const menuItemClass =
    'justify-start text-center text-sm font-semibold text-black-200 hover:underline dark:text-black-100'

  return (
    <div>
      <h2 className="mb-3 text-lg font-semibold text-black-700 dark:text-white-500">Membership</h2>
      {loading ? (
        <SkeletonBox className="h-16 w-full" />
      ) : (
        <div>
          <p className="mb-6 text-sm leading-relaxed text-black-600 dark:text-white-500">
            You&apos;ve been a member of {membership.name} since&nbsp;
            {formatDate({ date: new Date(userMembership.createdAt), format: 'MMM do, y' })}.<br />
            {nextInvoice && (
              <>
                Next payment: {formatDate({ date: new Date(nextInvoice.period_end * 1000), format: 'MMM do, y' })} -{' '}
                {formatPaymentMethod(subscription.default_payment_method)}
              </>
            )}
          </p>
          {nextInvoice ? (
            <>
              <Separator className="my-6" />
              <div className="flex flex-col space-y-4">
                <div className="w-full">
                  <Button
                    variant="inline"
                    className={menuItemClass}
                    onClick={() => setSection(MembershipSettingsSections.CONTACT_SUPPORT)}
                  >
                    <Inbox20Icon className="mr-2" /> Contact support
                  </Button>
                </div>
                <div className="w-full">
                  <Button
                    variant="inline"
                    className={menuItemClass}
                    onClick={() => setSection(MembershipSettingsSections.MANAGE_MEMBERSHIP)}
                  >
                    <Settings24Icon className="mr-2 h-5 w-5" />
                    Manage membership
                  </Button>
                </div>
              </div>
            </>
          ) : (
            <Button onClick={() => setSection(MembershipSettingsSections.CANCEL_MEMBERSHIP)} variant="outline">
              Leave Community
            </Button>
          )}
        </div>
      )}
    </div>
  )
}
