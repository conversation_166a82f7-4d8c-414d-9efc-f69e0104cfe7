import Link from 'next/link'

import { But<PERSON> } from '@/components/ui'
import { useStore } from '@/hooks/useStore'
import { getCommunityBaseURL } from '@/src/libs/utils'

export function MembershipContactSupport({ onBack }: { onBack: () => void }) {
  const membership = useStore((state) => state.community.membership)

  return (
    <div>
      <h2 className="mb-3 text-base font-semibold text-black-700 dark:text-white-500">Support</h2>
      <p className="mb-6 text-sm text-black-600 dark:text-black-100">
        If you need help, you can DM the&nbsp;
        <Link className="text-community-primary" href={`${getCommunityBaseURL(membership)}/members?tab=admins`}>
          community admins
        </Link>
        {membership.membership_setting?.support_email && (
          <>
            or email&nbsp;
            <Link className="text-community-primary" href={`mailto:${membership.membership_setting.support_email}`}>
              {membership.membership_setting.support_email}
            </Link>
          </>
        )}
      </p>
      <Button onClick={onBack} variant="outline">
        Got it
      </Button>
    </div>
  )
}
