import EventEmitter from 'events'
import { NextApiRequest, NextApiResponse } from 'next'
import { createRequest, createResponse, RequestMethod } from 'node-mocks-http'

import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'

export type MockedUser = {
  id: string
  role: USER_ROLE_ENUM
  current_membership_id?: string
}

export const mockRequestAndResponse = (method: RequestMethod, url?: string) => {
  return {
    req: mockRequest(method, url),
    res: mockResponse(),
  }
}

export const mockRequest = (method: RequestMethod, url?: string) => {
  return createRequest<NextApiRequest>({
    method,
    url,
  })
}

export const mockResponse = () => {
  return createResponse<NextApiResponse>({ eventEmitter: EventEmitter })
}
