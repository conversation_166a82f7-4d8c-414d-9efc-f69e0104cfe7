import handler from '@/memberup/pages/api/membership/list'
import { mockRequestAndResponse } from '@/memberup/test/utils'

describe('/api/membership/list', () => {
  describe('GET', () => {
    it('should return only discoverable memberships from database', async () => {
      const { req, res } = mockRequestAndResponse('GET', '/api/membership/list')

      await handler(req, res)

      expect(res._getStatusCode()).toBe(200)

      const responseData = JSON.parse(res._getData())
      expect(responseData.success).toBe(true)
      expect(responseData.data).toHaveProperty('memberships')

      const memberships = responseData.data.memberships.docs

      const returnedIds = memberships.map((m: any) => m.id)
      expect(returnedIds).toContain('1')
      expect(returnedIds).toContain('2')
      expect(returnedIds).not.toContain('3')

      memberships.forEach((membership: any) => {
        expect(membership).toHaveProperty('membership_setting')
        expect(membership.membership_setting.discoverable).toBe(true)
      })

      expect(responseData.data.memberships.total).toBeGreaterThanOrEqual(2)
    })
  })
})
