import { USER_MEMBERSHIP_STATUS_ENUM, USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import handler from '@/memberup/pages/api/membership/user-membership'
import { data } from '@/memberup/test/fixtures/data'
import { mockRequestAndResponse } from '@/memberup/test/utils'

jest.mock('../../../src/middlewares/authentication', () => {
  return jest.fn((req, res, next) => {
    req.user = data.user[1]
    next()
  })
})

describe('/api/membership/user-membership', () => {
  describe('GET', () => {
    it('should return user membership when it exists', async () => {
      const { req, res } = mockRequestAndResponse('GET', '/api/membership/user-membership')
      req.query = { membership_id: '1' }

      await handler(req, res)

      expect(res._getStatusCode()).toBe(200)

      const responseData = JSON.parse(res._getData())
      expect(responseData.success).toBe(true)
      expect(responseData.data).toEqual(
        expect.objectContaining({
          id: '1',
          user_id: '2',
          membership_id: '1',
          user_role: USER_ROLE_ENUM.member,
          status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
        }),
      )
    })

    it('should return 404 when user membership does not exist', async () => {
      const { req, res } = mockRequestAndResponse('GET', '/api/membership/user-membership')
      req.query = { membership_id: '2' }

      await handler(req, res)

      expect(res._getStatusCode()).toBe(404)

      const responseData = JSON.parse(res._getData())
      expect(responseData.success).toBe(false)
      expect(responseData.message).toBe('UserMembership not found')
    })

    it('should handle non-GET methods with 404', async () => {
      const { req, res } = mockRequestAndResponse('POST', '/api/membership/user-membership')

      await handler(req, res)

      expect(res._getStatusCode()).toBe(404)
      expect(res._getData()).toBe('Api is not found')
    })
  })
})
