'use client'

import { create } from 'zustand'
import { computed } from 'zustand-computed-state'
import { devtools } from 'zustand/middleware'

import { AuthSlice, AuthSliceState, createAuthSlice } from './authSlice'
import { CommunitySlice, CommunitySliceState, createCommunitySlice } from './communitySlice'
import { createStreamChatSlice, StreamChatSlice, StreamChatSliceState } from './streamChatSlice'
import { createUISlice, UISlice, UISliceState } from './uiSlice'
import { createFeedSlice, FeedSlice, FeedSliceState } from '@/store/feedSlice'

export type StoreState = AuthSlice & CommunitySlice & UISlice & FeedSlice & StreamChatSlice

export interface InitialStoreState {
  auth: AuthSliceState
  community: Partial<CommunitySliceState>
  feed: Partial<FeedSliceState>
  streamChat: StreamChatSliceState
  ui: UISliceState
}

export const createStore = (initialState: Partial<InitialStoreState>) => {
  return create<StoreState>()(
    devtools(
      computed((...a) => ({
        ...createAuthSlice(initialState.auth)(...a),
        ...createCommunitySlice(initialState.community)(...a),
        ...createFeedSlice(initialState.feed)(...a),
        ...createUISlice()(...a),
        ...createStreamChatSlice(initialState.streamChat)(...a),
      })),
    ),
  )
}
