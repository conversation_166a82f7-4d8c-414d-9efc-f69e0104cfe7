import { headers } from 'next/headers'
import React from 'react'

import { auth } from '@/auth'

import 'react-h5-audio-player/lib/styles.css'

import { Slide, ToastContainer } from 'react-toastify'

import 'react-toastify/dist/ReactToastify.css'
import 'slick-carousel/slick/slick.css'
import 'slick-carousel/slick/slick-theme.css'

import { Toaster } from 'sonner'

import 'stream-chat-react/dist/css/v2/index.css'

import { z } from 'zod'

import { RootLayoutProviders } from '@/components/providers'
import { PostHogProvider } from '@/components/providers/PostHogProvider'

import '@memberup/shared/dist/styles/globals.scss'

import { getCachedAuthenticatedUserData } from '@/lib/server-components/users'
import { getCachedCommunityData } from '@/lib/server/communities'
import { customErrorMap } from '@/lib/validation/zod'
import { InitialStoreState } from '@/store'

import './ui/global.css'

import { checkRouteProtection } from '@/lib/server/route-protection'
import { updateUserLastActivity } from '@/lib/server/users'

z.setErrorMap(customErrorMap)

export default async function RootLayout({ children }: { children: React.ReactNode }) {
  const requestHeaders = await headers()
  const session = await auth()
  const pathname = requestHeaders.get('x-pathname')
  let userData = null
  let membershipData = null
  let communityOwner = null

  await checkRouteProtection(pathname)

  if (session?.user?.id) {
    await updateUserLastActivity(session.user.id)
    userData = await getCachedAuthenticatedUserData(session.user.id)
  }

  const communitySlug = requestHeaders.get('x-community-slug')

  if (communitySlug) {
    membershipData = await getCachedCommunityData(communitySlug)
    communityOwner = membershipData?.owner
  }

  const initialData: Partial<InitialStoreState> = {
    auth: {
      showForm: null,
      ...userData,
    },
    community: {
      membership: membershipData,
      overrideThemeMainColor: null,
      owner: communityOwner,
      loadingMessages: true,
      newlyCreatedId: null,
    },
    feed: {
      spaces: membershipData?.channels,
    },
    ui: {
      navbarOffset: 0,
      stickyItemsOffset: 0,
      searchOpen: false,
    },
  }

  return (
    <html lang="en">
      <PostHogProvider>
        <RootLayoutProviders initialData={initialData}>
          {children}
          <Toaster position="top-right" offset={100} />
          <ToastContainer
            autoClose={3000}
            pauseOnHover={false}
            pauseOnFocusLoss={false}
            hideProgressBar={true}
            transition={Slide}
          />
        </RootLayoutProviders>
      </PostHogProvider>
    </html>
  )
}

export const metadata = {
  title: {
    default: 'MemberUp',
    template: '%s',
  },
  description: 'Where creators & brands build extraordinary communities',
}
