'use client'

import { SignUpForm } from '@/components/auth/signup-form'
import { ParticlesContainer } from '@/components/layout/particles-container'
import { PrimaryBlurredBackground } from '@/components/layout/primary-blurred-background'

export default function SignUp() {
  return (
    <>
      <PrimaryBlurredBackground className="-top-20" />
      <ParticlesContainer className="relative z-20 mx-auto w-full rounded-base border border-grey-900 px-4 py-8 md:mx-0 md:px-5 md:py-8 lg:px-6">
        <SignUpForm mode="standalone" />
      </ParticlesContainer>
      <PrimaryBlurredBackground className="-bottom-[19rem]" />
    </>
  )
}
