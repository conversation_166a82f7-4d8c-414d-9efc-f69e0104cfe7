import Link from 'next/link'

export default function CookiePolicy() {
  return (
    <section className="max-w-[59.5625rem] rounded-xl bg-black-500 p-6 shadow">
      <h1 className="mb-6 text-[1.375rem] font-bold">Cookie policy</h1>

      <p className="mb-6 text-sm sm:text-sbase">
        To make this site work properly, we sometimes place small data files called cookies on your device. A cookie is
        a small text file that a website saves on your computer or mobile device when you visit the site. It enables the
        website to remember your actions and preferences (such as login, language, or other preferences) over a period
        of time, so you do not have to keep re-entering them whenever you come back to the site or browse from one page
        to another.
      </p>

      <h2 className="mb-4 text-lg font-semibold">Cookies on MemberUp.com</h2>
      <p className="mb-6 text-sm sm:text-sbase">
        The table below outlines the different categories of cookies that our site uses, and why we use them. The lists
        of third-party cookie providers are intended merely as illustrative and should not be viewed as a comprehensive
        list.
      </p>

      <div className="mb-8 overflow-x-auto">
        <table className="min-w-full rounded-lg border border-black-400 text-left text-[13px] sm:text-sbase">
          <thead className="bg-black-400">
            <tr>
              <th className="px-2 py-2 font-semibold sm:px-4 sm:py-3">Type of Cookie</th>
              <th className="px-2 py-2 font-semibold sm:px-4 sm:py-3">Purpose</th>
              <th className="hidden px-2 py-2 font-semibold sm:table-cell sm:px-4 sm:py-3">Who Serves</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-t border-black-400">
              <td className="px-2 py-2 sm:px-4 sm:py-3">
                <span className="block sm:hidden">Strictly Necessary (1)</span>
                <span className="hidden sm:block">Strictly Necessary</span>
              </td>
              <td className="px-2 py-2 sm:px-4 sm:py-3">
                Required for core platform functionality such as logging in, session management, and account security.
              </td>
              <td className="hidden px-2 py-2 sm:table-cell sm:px-4 sm:py-3">
                <Link
                  href="https://memberup.com/"
                  className="text-primary-100"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  MemberUp.com
                </Link>
              </td>
            </tr>
            <tr className="border-t border-black-400">
              <td className="px-2 py-2 sm:px-4 sm:py-3">
                <span className="block sm:hidden">Functionality (2)</span>
                <span className="hidden sm:block">Functionality</span>
              </td>
              <td className="px-2 py-2 sm:px-4 sm:py-3">
                Used to remember user preferences and support features like video playback position.
              </td>
              <td className="hidden px-2 py-2 sm:table-cell sm:px-4 sm:py-3">
                <Link
                  href="https://www.mux.com/privacy"
                  className="text-primary-100"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  Mux
                </Link>
              </td>
            </tr>
          </tbody>
        </table>
        {/* Mobile only: Who Serves summary */}
        <div className="mt-4 block text-[13px] sm:hidden sm:text-sbase">
          <div className="mb-2 font-semibold">Who Serves:</div>
          <div className="mb-1">
            (1) Strictly Necessary:{' '}
            <Link href="https://memberup.com/" className="text-primary-100" target="_blank" rel="noopener noreferrer">
              MemberUp.com
            </Link>
          </div>
          <div>
            (2) Functionality:{' '}
            <Link
              href="https://www.mux.com/privacy"
              className="text-primary-100"
              target="_blank"
              rel="noopener noreferrer"
            >
              Mux
            </Link>
          </div>
        </div>
      </div>

      <h2 className="mb-4 text-lg font-semibold">Cookies and You</h2>
      <p className="mb-6 text-sm sm:text-sbase">
        To create a MemberUp account, cookies must be enabled in your web browser. You can still browse the site without
        enabling them, but certain features and functionality may not be available or may not work as intended.
      </p>

      <h2 className="mb-4 text-lg font-semibold">Controlling Your Cookies</h2>
      <p className="mb-6 text-sm sm:text-sbase">
        You can manage cookies directly through your browser settings—either to block them entirely or to control how
        they&apos;re used. If you&apos;d like to limit or prevent cookies from any website, adjust the settings in each
        browser you use, across all your devices.
      </p>
      <p className="mb-6 text-sm sm:text-sbase">
        For broader control over advertising cookies, you can also use opt-out tools from industry groups in your
        region—such as{' '}
        <Link
          href="https://www.aboutads.info/choices/"
          className="text-primary-100"
          target="_blank"
          rel="noopener noreferrer"
        >
          aboutads.info
        </Link>{' '}
        in the U.S. or{' '}
        <Link
          href="https://www.youronlinechoices.com/uk/your-ad-choices"
          className="text-primary-100"
          target="_blank"
          rel="noopener noreferrer"
        >
          Your Online Choices
        </Link>{' '}
        in the EU.
      </p>
    </section>
  )
}
