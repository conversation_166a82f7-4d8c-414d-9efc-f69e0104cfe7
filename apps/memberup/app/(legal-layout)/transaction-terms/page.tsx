import Link from 'next/link'

export default function TransactionTerms() {
  return (
    <section className="max-w-[59.5625rem] rounded-xl bg-black-500 p-6 shadow">
      <h1 className="mb-6 text-[1.375rem] font-bold">Transaction terms</h1>

      <h2 className="mb-4 text-lg font-semibold">1. Introduction</h2>
      <p className="mb-6 text-sm sm:text-sbase">
        These Transaction Terms between Admins and Members (&quot;Transaction Terms&quot;) govern all transactions
        between an Admin and a Member (as defined below) on MemberUp.
      </p>

      <h2 className="mb-4 text-lg font-semibold">2. Applicability of Transaction Terms</h2>
      <p className="mb-6 text-sm sm:text-sbase">
        Each time an Admin/Member Transaction (as defined below) is initiated on MemberUp, these Transaction Terms will
        govern the transaction exclusively, overriding any additional or conflicting terms proposed by either party.
        These Transaction Terms are binding on the Admin and Member participating in the Admin/Member Transaction.
        MemberUp&apos;s [Privacy Policy] and [Terms and Conditions] are incorporated by reference with full force and
        effect. If there is any conflict between those policies and these Transaction Terms, these Terms will control
        with respect to the Admin/Member Transaction.
      </p>

      <h2 className="mb-4 text-lg font-semibold">3. Parties</h2>
      <p className="mb-6 text-sm sm:text-sbase">
        The only parties to these Transaction Terms are the Admin and the Member involved in the Admin/Member
        Transaction. MemberUp, Inc., and its affiliates do not grant any rights in, nor participate in, any Admin/Member
        Transaction, except as a payment intermediary in accordance with the Admin&apos;s or Member&apos;s instructions,
        as outlined in Section 5 of these Transaction Terms.
      </p>

      <h2 className="mb-4 text-lg font-semibold">4. Definitions</h2>
      <p className="mb-6 text-sm sm:text-sbase">In these Transaction Terms, the following definitions apply:</p>

      <ul className="mb-6 list-disc space-y-4 pl-8 text-sm sm:text-sbase">
        <li>
          <span className="font-semibold">&quot;MemberUp&quot;</span> refers to the MemberUp platform, including its
          website, applications, and subdomains;
        </li>
        <li>
          <span className="font-semibold">&quot;Content&quot;</span> means any material uploaded to MemberUp by any User
          (as defined in MemberUp&apos;s Terms and Conditions), including but not limited to videos, audio, text,
          livestreams, images, files, links, and other media;
        </li>
        <li>
          <span className="font-semibold">&quot;Admin&quot;</span> means a User who owns or manages a community on
          MemberUp and is responsible for providing access to content, memberships, or other offerings;
        </li>
        <li>
          <span className="font-semibold">&quot;Member&quot;</span> means a User who joins or purchases access to a
          community managed by an Admin;
        </li>
        <li>
          <span className="font-semibold">&quot;Admin/Member Transaction&quot;</span> refers to any transaction between
          an Admin and a Member, including the purchase of access, content, memberships, or other offerings;
        </li>
        <li>
          <span className="font-semibold">&quot;Member Payment&quot;</span> means any and all payments made by a Member
          in respect of any Admin/Member Transaction;
        </li>
        <li>
          <span className="font-semibold">&quot;Subscription&quot;</span> means a Member&apos;s subscription to an
          Admin&apos;s community.
        </li>
      </ul>

      <h2 className="mb-4 text-lg font-semibold">5. Payments, Subscriptions, and Renewals</h2>

      <p className="mb-4 text-sm sm:text-sbase">
        a. By entering into an Admin/Member Transaction, the Member agrees to pay the Member Payment associated with the
        applicable transaction, whether structured as a subscription, a one-time payment, or a payment plan, as
        displayed on the relevant Admin&apos;s community about page. The Member and Admin each authorize MemberUp, Inc.,
        or any of its subsidiaries or affiliates, to act as a limited payment collection agent for the Admin, with the
        authority to collect, hold, and disburse Member Payments in accordance with these Terms.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        b. Admins are solely responsible for determining, within platform parameters, the pricing structure of their
        offerings (including subscriptions, one-time payments, and payment plans), and the corresponding access or
        Content to be granted to Members.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        c. All pricing is listed and processed exclusively in United States Dollars (USD). Additional fees for currency
        conversion may be charged by the Member&apos;s card issuer or financial institution.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        d. To initiate a transaction, Members must add a valid payment method to their MemberUp account and complete the
        transaction by selecting the applicable purchase or subscription option within the Admin&apos;s community page.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        e. By entering into these Transaction Terms, the Member authorizes MemberUp, Inc., and its affiliates to
        securely transmit the Member&apos;s payment information to third-party payment processors, including but not
        limited to Stripe, Inc., for the purpose of processing Member Payments. The use of these processors is governed
        by their respective terms and conditions, including Stripe&apos;s{' '}
        <Link href="https://stripe.com/us/legal" className="text-primary-100" target="_blank" rel="noopener noreferrer">
          US Services Agreement
        </Link>{' '}
        and{' '}
        <Link
          href="https://stripe.com/connect-account/legal"
          className="text-primary-100"
          target="_blank"
          rel="noopener noreferrer"
        >
          Connected Account Agreement
        </Link>
        , as amended from time to time. The Member expressly agrees and undertakes to be bound by such third-party
        terms. MemberUp has no control over, and disclaims all responsibility for, any changes to such third-party
        terms, fees, or policies—including any currency conversion charges, international transaction fees, or
        processing delays imposed by the Member&apos;s card issuer or bank.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        f. If a Member has more than one payment card on file, and the initial payment method is declined, MemberUp
        reserves the right to charge any alternative payment method provided by the Member to satisfy the payment
        obligation.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        g. Payments will be collected as follows: (i) recurring charges for Subscriptions, automatically billed at the
        start of each billing cycle; (ii) one-time charges, collected immediately upon purchase confirmation; (iii)
        installment charges for payment plans, automatically billed according to the installment schedule set by the
        Admin. The Member hereby authorizes all such charges and acknowledges that payment plans are binding commitments
        to pay the full amount over the defined schedule.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        h. Subscriptions will automatically renew at the end of each billing period unless: (i) the Member&apos;s
        payment method is declined, (ii) the subscription price has changed, or (iii) the Member cancels the
        Subscription in advance. One-time payments and payment plans do not renew automatically.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        i. Cancellation. Subscriptions may be canceled at any time by MemberUp or the Admin unless otherwise specified
        in a promotional offer. Upon cancellation, all fees due through the current billing cycle will remain payable.
        No refunds will be provided for partial billing periods unless expressly stated. Members will retain access to
        Content until the end of the paid term. Payment plans and one-time purchases are non-cancelable and
        non-refundable unless otherwise stated in writing.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">Members may cancel Subscriptions by:</p>
      <ul className="mb-4 list-disc space-y-2 pl-8 text-sm sm:text-sbase">
        <li>Clicking &quot;Cancel Membership&quot; in the Memberships tab of their user settings;</li>
        <li>Contacting the Admin directly; or</li>
        <li>Contacting <NAME_EMAIL>.</li>
      </ul>

      <p className="mb-4 text-sm font-semibold sm:text-sbase">
        SUBSCRIPTION PAYMENTS AUTOMATICALLY RENEW FOR INDEFINITE SUCCESSIVE BILLING PERIODS OF EQUAL DURATION UNLESS
        CANCELED IN ADVANCE BY THE MEMBER, ADMIN, OR MEMBERUP. TO AVOID BEING CHARGED FOR THE NEXT CYCLE, THE MEMBER
        MUST CANCEL THE SUBSCRIPTION PRIOR TO THE END OF THE THEN-CURRENT BILLING PERIOD BY (i) CLICKING &quot;CANCEL
        MEMBERSHIP&quot; IN THE MEMBERSHIP TAB OF THEIR SETTINGS, (ii) CONTACTING THE ADMIN OF THE COMMUNITY, OR (iii)
        CONTACTING <NAME_EMAIL>. FOR SUBSCRIPTIONS THAT INCLUDE A FREE TRIAL, UNLESS THE MEMBER
        CANCELS BEFORE THE TRIAL PERIOD ENDS, THE FIRST PAYMENT WILL BE CHARGED IMMEDIATELY AFTER THE FREE TRIAL
        EXPIRES, AND THE SUBSCRIPTION WILL AUTOMATICALLY CONVERT INTO A PAID, RECURRING SUBSCRIPTION OF THE TYPE
        ASSOCIATED WITH THE TRIAL OFFER. ONE-TIME PAYMENTS AND PAYMENT PLANS ARE NON-RECURRING AND REMAIN PAYABLE
        THROUGH THE DURATION OF THE AGREED INSTALLMENT SCHEDULE.
      </p>

      <p className="mb-4 text-sm sm:text-sbase">
        j. If a Member cancels a Subscription, the Member will retain access to the applicable Admin&apos;s Content
        until the end of the then-current billing period. No further charges will be made for that Subscription after
        the billing period ends, unless the Member initiates a new Subscription. After the billing period concludes, the
        Member&apos;s access to the Admin&apos;s Content will automatically terminate.
      </p>

      <p className="mb-6 text-sm sm:text-sbase">
        k. The Member agrees not to initiate any unjustified refund requests or chargebacks in connection with any
        Admin/Member Transaction. All chargeback attempts must be based on legitimate grounds. Abuse of the chargeback
        process may result in suspension or termination of the Member&apos;s account.
      </p>

      <h2 className="mb-4 text-lg font-semibold">6. License of Content</h2>
      <p className="mb-4 text-sm sm:text-sbase">
        Upon receipt of the applicable Member Payment for an Admin/Member Transaction, the Admin grants the Member a
        limited, revocable license to access the specific Content made available under that transaction (&quot;Relevant
        Content&quot;). This license is:
      </p>
      <ul className="mb-4 list-disc space-y-2 pl-8 text-sm sm:text-sbase">
        <li>Non-exclusive,</li>
        <li>Non-transferable,</li>
        <li>Non-sublicensable, and</li>
        <li>Granted solely for the Member&apos;s personal, private, non-commercial, and non-promotional use.</li>
      </ul>
      <p className="mb-6 text-sm sm:text-sbase">
        Access is permitted only through the Member&apos;s personal device via a standard web browser or approved
        application. The Member may make a temporary, incidental copy of the Relevant Content solely as part of the
        technical process required to access or stream such Content (e.g., browser caching), and only in accordance with
        MemberUp&apos;s Acceptable Use Policy.
      </p>

      <h2 className="mb-4 text-lg font-semibold">7. Ownership of Content</h2>
      <p className="mb-6 text-sm sm:text-sbase">
        The Member acknowledges and agrees that the license granted under Section 6 does not confer any ownership or
        intellectual property rights in the Relevant Content. All rights, title, and interest in and to the Relevant
        Content remain solely with the Admin who provided it.
      </p>

      <h2 className="mb-4 text-lg font-semibold">8. Expiration of License</h2>
      <p className="mb-4 text-sm sm:text-sbase">
        The license granted to a Member under Section 6 will expire automatically, without notice, under any of the
        following circumstances:
      </p>
      <ul className="mb-6 space-y-2 pl-8 text-sm sm:text-sbase">
        <li>
          a. If the Member Payment associated with the Admin/Member Transaction is unsuccessful, reversed, or subject to
          a chargeback for any reason;
        </li>
        <li>
          b. Upon the expiration of the paid access period associated with a Subscription, one-time purchase, or payment
          plan, unless the Subscription is renewed or a new transaction is completed;
        </li>
        <li>c. If the Member&apos;s account is suspended or terminated for any reason;</li>
        <li>
          d. If the Member violates MemberUp&apos;s Acceptable Use Policy, whether in connection with the Relevant
          Content or otherwise;
        </li>
        <li>e. If the Relevant Content is removed from the Admin&apos;s community; or</li>
        <li>f. If the Member voluntarily closes their MemberUp account.</li>
      </ul>

      <h2 className="mb-4 text-lg font-semibold">9. Cancellation and Refunds</h2>
      <p className="mb-4 text-sm sm:text-sbase">
        MemberUp does not offer refunds for Admin/Member Transactions. Admins are solely responsible for setting and
        enforcing their own refund policies. If a Member wishes to request a refund, they must contact the Admin
        directly using the support contact provided in their receipt or within the community.
      </p>
      <p className="mb-6 text-sm sm:text-sbase">
        MemberUp reserves the right, in its sole discretion, to issue refunds in exceptional circumstances. However,
        MemberUp is not obligated to mediate disputes or process refunds on behalf of Admins, and in most cases will not
        intervene in Admin/Member Transactions.
      </p>

      <h2 className="mb-4 text-lg font-semibold">10. Obligations between Admin and Member</h2>
      <p className="mb-4 text-sm sm:text-sbase">For each Admin/Member Transaction:</p>
      <ul className="mb-6 space-y-2 pl-8 text-sm sm:text-sbase">
        <li>
          a. The Admin and the Member agree to comply at all times with MemberUp&apos;s Acceptable Use Policy in
          connection with the Relevant Content, including when accessing, viewing, sharing, or interacting with it.
        </li>
        <li>
          b. The Member agrees to make the applicable Member Payment in order to access, view, or interact with the
          Relevant Content, and agrees not to initiate a chargeback unless disputing the transaction in good faith.
        </li>
        <li>
          c. The Admin agrees to make the Relevant Content available to the Member upon receipt of the corresponding
          Member Payment.
        </li>
        <li>
          d. The Admin warrants that it holds all rights, licenses, and permissions necessary to distribute the Relevant
          Content and to grant the license set out in Section 6, including in the territory where the Content is made
          available.
        </li>
        <li>
          e. The Admin is solely responsible for creating and uploading the Relevant Content. MemberUp does not
          guarantee that an Admin will continue to produce or make Content available on an ongoing basis.
        </li>
        <li>
          f. Unless caused by the Admin&apos;s negligence or breach of duty, access to the Admin&apos;s Content by the
          Member is at the Member&apos;s own risk.
        </li>
      </ul>

      <h2 className="mb-4 text-lg font-semibold">11. No Guarantees</h2>
      <p className="mb-4 text-sm sm:text-sbase">
        The Member acknowledges that Admins may add, modify, or remove Content from their community at any time, at
        their sole discretion. Admins are not required to maintain any particular amount or type of Content, and
        MemberUp does not guarantee the availability, consistency, or continuation of any specific Content.
      </p>
      <p className="mb-4 text-sm sm:text-sbase">
        The Member further acknowledges that there may be circumstances in which access to Relevant Content becomes
        unavailable, including but not limited to:
      </p>
      <ul className="mb-6 space-y-2 pl-8 text-sm sm:text-sbase">
        <li>a. If the Admin&apos;s account is suspended or deleted;</li>
        <li>b. If the Member&apos;s account is suspended or deleted;</li>
        <li>c. If any part of the MemberUp platform becomes unavailable or is suspended; or</li>
        <li>d. If the Admin is no longer able or willing to create or upload Content.</li>
      </ul>

      <h2 className="mb-4 text-lg font-semibold">12. Disputes Between Admin and Member</h2>
      <p className="mb-6 text-sm sm:text-sbase">
        If a dispute arises between an Admin and a Member in connection with an Admin/Member Transaction or these
        Transaction Terms, both parties acknowledge and agree that MemberUp, Inc. has no obligation to intervene,
        mediate, or resolve the dispute. MemberUp is not liable for the relationship between the Admin and the Member,
        and will not provide legal advice or support in relation to such matters.
      </p>
    </section>
  )
}
