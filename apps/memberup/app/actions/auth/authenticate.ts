'use server'

import * as Sentry from '@sentry/nextjs'

import {
  BannedUserError,
  DeletedUserError,
  EmailAlreadyExistsError,
  InvalidCredentialsError,
  InvalidLoginDataError,
  InvalidSignUpDataError,
  signIn,
} from '@/auth'

export async function authenticate(credentials: any) {
  try {
    return await signIn('credentials', credentials)
  } catch (error) {
    if (error instanceof InvalidCredentialsError) {
      return {
        errorCode: 'invalid_credentials',
        success: false,
      }
    }

    if (error instanceof EmailAlreadyExistsError) {
      return {
        errorCode: 'email_already_exists',
        success: false,
      }
    }

    if (error instanceof InvalidSignUpDataError) {
      return {
        errorCode: 'invalid_sign_up_data',
        success: false,
        message: error.message,
      }
    }

    if (error instanceof InvalidLoginDataError) {
      return {
        errorCode: 'invalid_login_data',
        success: false,
        message: error.message,
      }
    }

    if (error instanceof BannedUserError) {
      return {
        errorCode: 'banned_user',
        success: false,
      }
    }

    if (error instanceof DeletedUserError) {
      return {
        errorCode: 'deleted_user',
        success: false,
      }
    }
    Sentry.captureException(error)
    console.error(error)
    throw new Error()
  }
}
