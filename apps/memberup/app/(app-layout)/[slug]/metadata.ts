import { getCachedCommunityData } from '@/lib/server/communities'
import { extractOpenGraphFromHTML } from '@/lib/server/sanitization'
import { getUserPublicDataByUsername } from '@/lib/server/users'

export function isProfile(slug: string) {
  return slug.startsWith('%40')
}

export async function generateProfileMetadata(slug: string) {
  const username = slug.replace('%40', '')
  const userData = await getUserPublicDataByUsername(username)
  const userFullName = `${userData?.first_name} ${userData?.last_name}`
  const defaultDescription = `View ${userFullName}'s profile on MemberUp.`
  const description = userData?.profile?.bio?.length ? userData?.profile.bio : defaultDescription

  const ogImage = userData?.profile?.image

  return {
    title: `${userFullName} - Profile`,
    description,
    alternates: {
      canonical: `/@${username}`,
    },
    openGraph: {
      title: `${userFullName} on MemberUp.`,
      description,
      type: 'profile',
      siteName: 'MemberUp',
      images: ogImage ? [ogImage] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title: `${userFullName}`,
      description,
      images: ogImage ? [ogImage] : [],
    },
  }
}

export async function generateCommunityMetadata(slug: string) {
  const membershipData = await getCachedCommunityData(slug)
  const communityDescription =
    membershipData?.membership_setting?.about_text ?? membershipData?.membership_setting?.description
  const ogDescription = communityDescription ? await extractOpenGraphFromHTML(communityDescription) : ``
  const ogImage = membershipData?.membership_setting?.cover_image
  const title = `${membershipData?.name || slug}`

  return {
    title,
    description: ogDescription,
    alternates: {
      canonical: `/${slug}`,
    },
    openGraph: {
      title,
      description: ogDescription,
      type: 'website',
      siteName: 'MemberUp',
      images: ogImage ? [ogImage] : [],
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description: ogDescription,
    },
  }
}
