import { Feed } from '@/components/feed/feed'
import { UserProfileServerComponent } from '@/components/profile/UserProfileServerComponent'

const isProfile = (slug: string) => {
  return slug.startsWith('%40')
}

export default async function Page(props: { params: Promise<{ slug: string }> }) {
  const params = await props.params

  if (isProfile(params.slug)) {
    return <UserProfileServerComponent params={params} />
  }

  return <Feed />
}

export async function generateMetadata(props: { params: Promise<{ slug: string }> }) {
  const params = await props.params
  return {
    title: isProfile(params.slug) ? 'Profile' : 'Community',
  }
}
