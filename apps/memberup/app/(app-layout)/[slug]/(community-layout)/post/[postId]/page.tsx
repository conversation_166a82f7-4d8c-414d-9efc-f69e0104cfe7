'use client'

import useTheme from '@mui/material/styles/useTheme'
import Image from 'next/image'
import { useParams, useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Channel, useChatContext } from 'stream-chat-react'

import { slugify } from '@memberup/shared/src/libs/string-utils'
import { getChannelApi } from '@memberup/shared/src/services/apis/channel.api'
import { CommunityDetails } from '@/components/community/community-details'
import { PostDetail } from '@/components/feed/post-detail'
import { useStore } from '@/hooks/useStore'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import { isUUIDv4 } from '@/memberup/libs/utils'
import { setPostChannelName } from '@/memberup/store/features/headerSlice'
import { useAppDispatch } from '@/memberup/store/store'

async function getPostIdByPermalink(permalink) {
  /* encode permalink for url send */
  const encodedPermalink = encodeURIComponent(permalink)
  const result = await fetch(`/api/feed?permalink=${encodedPermalink}`).then((res) => res.json())
  return result
}

function SinglePostContent() {
  const membership = useStore((state) => state.community.membership)
  const router = useRouter()
  const params = useParams()
  const postId = params.postId
  const dispatch = useAppDispatch()
  const [post, setPost] = useState<any>(null)
  const [channel, setChannel] = useState<any>(null)
  const [error, setError] = useState<any>(null)
  const { client } = useChatContext()

  const POST_NOT_FOUND_ERROR = 'Post not found!'

  useEffect(() => {
    async function fetchFeed() {
      setError(false)
      let postIdentifier: string = postId as string

      let isParameterPermalink = !isUUIDv4(postIdentifier)
      if (isParameterPermalink) {
        const result = await getPostIdByPermalink(postIdentifier)
        if (!result?.data?.id) {
          setError(POST_NOT_FOUND_ERROR)
          dispatch(setPostChannelName(null))
          return
        }
        postIdentifier = result.data.id
      }

      try {
        const result: any = await client.getMessage(postIdentifier)
        if (!result?.message || result.parent_id) {
          // We want to just display posts and not comments
          setError(POST_NOT_FOUND_ERROR)
        } else {
          const channelType = result.message.channel.type
          const channelId = result.message.channel.id
          const channelRef = client.channel(channelType, channelId)

          const handleUpdatedMessage = (event) => {
            if (result.message.id === event.message.id) {
              setPost(event.message)
            }
          }

          const handleDeletedMessage = (event) => {
            // TODO: This can be a dispatch command.
            if (result.message.id === event.message.id) {
              const sluggedChannel = slugify(result.message.channel.name)
              router.replace(sluggedChannel === 'community' ? '/community' : `/space/${sluggedChannel}`)
            }
          }

          channelRef.on('message.updated', handleUpdatedMessage)
          channelRef.on('message.deleted', handleDeletedMessage)

          setChannel(channelRef)
          setPost(result.message)

          const channel = await getChannelApi(channelId)
          const channelName = channel.data.data.name
          dispatch(setPostChannelName(channelName))

          // Cleanup function that will be called when the component unmounts or before re-running the effect
          return () => {
            channelRef.off('message.updated', handleUpdatedMessage)
            channelRef.off('message.deleted', handleDeletedMessage)
          }
        }
      } catch (e) {
        setError(POST_NOT_FOUND_ERROR)
        dispatch(setPostChannelName(null))
      }
    }

    if (!postId) {
      setError(POST_NOT_FOUND_ERROR)
      dispatch(setPostChannelName(null))
    }
    fetchFeed()
  }, [postId])

  let children = null
  if (post) {
    children = (
      <Channel channel={channel}>
        <div id="portal-suggestions-root" />
        <PostDetail
          membership={membership}
          feed={post}
          isPostPage={true}
          onPinMessage={() => {}}
          onUnpinMessage={() => {}}
        />
      </Channel>
    )
  }

  if (error) {
    children = (
      <div className={'flex w-full flex-col items-center justify-center'}>
        <Image src="/assets/default/images/icon.png" width={72} height={72} alt="Error Image" />
        <div className="flex-column flex w-full items-center justify-center text-sm font-bold">{error}</div>
      </div>
    )
  }

  return (
    <div className="space-y:6 page-inner-pb flex flex-col-reverse items-start md:grow md:flex-row md:space-x-6 md:space-y-0">
      <div className="w-full grow">{children}</div>
      <CommunityDetails />
    </div>
  )
}

export default function SinglePostPage() {
  return (
    <div className="relative">
      <SinglePostContent />
    </div>
  )
}
