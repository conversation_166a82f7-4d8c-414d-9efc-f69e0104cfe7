import { headers } from 'next/headers'
import Link from 'next/link'

import CommunityAboutPage from '@/components/community/about-page/about-page'
import { CommunityDetails } from '@/components/community/community-details'
import { cloudinaryLoader } from '@/lib/cloudinary'
import { getCachedCommunityData } from '@/lib/server/communities'
import { extractOpenGraphFromHTML } from '@/lib/server/sanitization'

const CommunityAboutPageLayout = async () => (
  <div className="flex w-full flex-col gap-5">
    <div className="mt-[-16px] flex flex-col-reverse items-start md:mt-0 md:flex-row md:space-x-6">
      <CommunityAboutPage />
      <CommunityDetails />
    </div>

    <Link
      href="/privacy-policy"
      target="_blank"
      rel="noopener noreferrer"
      className="mb-5 hidden text-sm text-black-200 dark:text-black-100 md:block"
    >
      Privacy and terms
    </Link>
  </div>
)

export default CommunityAboutPageLayout

export async function generateMetadata() {
  const requestHeaders = await headers()
  const communitySlug = requestHeaders.get('x-community-slug')
  const membershipData = await getCachedCommunityData(communitySlug)

  let image: null | { url: string; width: number; height: number; alt: string } = null

  if ((membershipData?.membership_setting?.about_gallery as Array<Object>)?.length > 0) {
    const firstImage = membershipData.membership_setting.about_gallery?.[0]

    if (firstImage?.url) {
      const thumbnailUrl = cloudinaryLoader({
        loaderOptions: {
          src: firstImage?.url,
          width: 1200,
          quality: 80,
        },
        preTransforms: `c_crop,w_1200,h_630`,
      })
      image = {
        url: thumbnailUrl,
        width: firstImage.width,
        height: firstImage.height,
        alt: membershipData.name,
      }
    } else if (firstImage?.mux_asset?.playback_ids?.[0]?.id) {
      image = {
        url: `https://image.mux.com/${firstImage?.mux_asset?.playback_ids?.[0]?.id}/thumbnail.jpg?width=1200&height=630&fit_mode=crop`,
        width: 1200,
        height: 630,
        alt: membershipData.name,
      }
    }
  }

  const aboutText = membershipData?.membership_setting?.about_text
  const ogDescription = await extractOpenGraphFromHTML(aboutText)

  return {
    title: membershipData?.name,
    description: membershipData?.membership_setting?.about_text,
    openGraph: {
      title: membershipData?.name,
      description: ogDescription,
      type: 'website',
      images: image ? [image] : [],
      siteName: 'MemberUp',
    },
    twitter: {
      card: 'summary_large_image',
      title: membershipData?.name,
      description: ogDescription,
      images: image ? [image.url] : [],
    },
  }
}
