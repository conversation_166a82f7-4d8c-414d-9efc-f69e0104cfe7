'use client'

import { <PERSON><PERSON><PERSON><PERSON>, Check } from '@mui/icons-material'
import {
  Accordion,
  AccordionDetails,
  AccordionSummary,
  Box,
  Button,
  Divider,
  IconButton,
  Tab,
  Tabs,
  Typography,
  useMediaQuery,
} from '@mui/material'
import parse from 'html-react-parser'
import { useParams, usePathname, useRouter, useSearchParams } from 'next/navigation'
import React, { useEffect, useState } from 'react'

import AppMuxPlayer from '@memberup/shared/src/components/common/app-mux-player'
import {
  getContentLibraryCourseApi,
  restartCourseApi,
  updateUserLessonStatusApi,
} from '@memberup/shared/src/services/apis/content-library.api'
import {
  VIDEO_TRANSCRIPTION_GENERAL_ERROR_MSG,
  VIDEO_TRANSCRIPTION_NO_SOUND_ERROR_MSG,
} from '@memberup/shared/src/types/consts'
import { useStore } from '@/hooks/useStore'
import AppAudioLibraryoPlayer from '@/memberup/components/common/app-audio-play-library'
import useAppTheme from '@/memberup/components/hooks/use-app-theme'
import useCheckUserRole from '@/memberup/components/hooks/use-check-user-role'
import FileAttachments from '@/memberup/components/list/file-attachments'
import CourseNavigator from '@/memberup/components/menu/course-navigator'
import SVGLeftArrow from '@/memberup/components/svgs/arrow-left'
import SVGClose from '@/memberup/components/svgs/close'
import SVGEye from '@/memberup/components/svgs/eyes'
import { roundCourseProgress } from '@/memberup/libs/utils'
import { getContentLibrary, selectContentLibrary } from '@/memberup/store/features/contentLibrarySlice'
import { selectMembership } from '@/memberup/store/features/membershipSlice'
import { selectUser, selectUserProfile } from '@/memberup/store/features/userSlice'
import { useAppDispatch, useAppSelector } from '@/memberup/store/hooks'

const formatTranscript = (transcript) => {
  //const sentences = transcript ? transcript.split(/(?<=[.!?])\s+/) : [] // tHis is generating issues with Safari
  const sentences = transcript ? transcript.split(/[.!?]\s+/) : []

  return sentences
    .reduce((acc, _, i) => (i % 3 === 0 ? acc.concat([sentences.slice(i, i + 3).join(' ')]) : acc), [])
    .map((paragraph, index) => (
      <p key={index}>
        {paragraph}
        <br />
        <br />
      </p>
    ))
}
function LessonDisplayMobile({
  selectedLesson,
  handleMarkLesson,
  setHidenSidebar,
  navigateToNextLesson,
  disableNextBtn,
  lessonText,
}: {
  selectedLesson: {
    id: string
    title: string
    done: boolean
    type: string
    media_file?: {
      id: string
      tracks?: Array<{
        max_height?: number
      }>
    }
    resource_files?: Array<{
      id: string
      name: string
      url: string
      type: string
    }>
    media_transcript?: string
    thumbnail_url?: string
    text?: string
  }
  handleMarkLesson: () => void
  setHidenSidebar: (value: boolean) => void
  navigateToNextLesson: () => void
  disableNextBtn: boolean
  lessonText: string | React.ReactNode
}) {
  const [tabValue, setTabValue] = useState('Description')
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)
  const { theme } = useAppTheme()

  const handleChange = (event, newValue) => {
    setTabValue(newValue)
  }

  return (
    <Box
      sx={{
        height: '100vh',
        backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#ffffff',
        paddingBottom: '54px !important',
        overflowY: 'scroll',
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '16px',
        }}
      >
        <Button
          onClick={() => setHidenSidebar(false)}
          sx={{
            color: '#8D94A3',
            borderRadius: '20px',
          }}
        >
          <SVGLeftArrow
            style={{
              marginRight: '6px',
            }}
          />
          <Typography sx={{ fontFamily: 'Graphik Regular', fontSize: '13px', color: 'rgb(141, 148, 163)' }}>
            Course Menu
          </Typography>
        </Button>
        <Button
          onClick={navigateToNextLesson}
          sx={{
            color: '#8D94A3',
            borderRadius: '20px',
          }}
          disabled={disableNextBtn}
        >
          <Typography sx={{ fontFamily: 'Graphik Regular', fontSize: '13px', color: 'rgb(141, 148, 163)' }}>
            Next
          </Typography>
          <SVGLeftArrow
            style={{
              transform: 'rotate(180deg)',
              marginLeft: '6px',
            }}
          />
        </Button>
      </Box>
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          padding: '0px 17px',
          marginBottom: '16px',
        }}
      >
        <Box
          sx={{
            width: '100%',
          }}
        >
          <Typography
            sx={{
              fontFamily: 'Graphik Semibold',
              fontSize: '24px',
              lineHeight: '32px',
              color: theme.palette.text.primary,
            }}
          >
            {selectedLesson?.title}
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'end',
            marginLeft: '18px',
          }}
        >
          <Button
            onClick={handleMarkLesson}
            sx={{
              borderRadius: '12px',
              backgroundColor: isDarkTheme ? 'rgba(255, 255, 255, 0.08)' : '#F3F5F5',
              boxShadow: 'none',
              color: selectedLesson.done ? '#AEE78B' : theme.palette.text.primary,
              height: '40px',
              width: '40px',
              minWidth: '40px',
              '&:hover': {
                backgroundColor: isDarkTheme ? '#434343' : '#43434350',
                boxShadow: 'none',
              },
            }}
          >
            {selectedLesson.done ? <Check /> : <Check />}
          </Button>
        </Box>
      </Box>
      {selectedLesson.type === 'video' && selectedLesson.media_file && (
        <Box
          sx={{
            height: 'auto',
            bg: '#f0f0f0',
          }}
        >
          <AppMuxPlayer
            autoPlay={false}
            asset={selectedLesson.media_file}
            wrapperStyle={{ width: '100%', height: '100%' }}
            playerStyle={{
              width: '100%',
              height: '100%',
              '--media-object-fit': 'contain',
              '--pip-button': 'none',
              aspectRatio: '16 / 9',
              backgroundColor: '#000000',
            }}
            onEnded={(e) => {}}
            streamType="on-demand"
            poster={selectedLesson.thumbnail_url}
          />
        </Box>
      )}
      {selectedLesson.type === 'audio' && selectedLesson.media_file && (
        <Box
          sx={{
            height: '400px',
            background: isDarkTheme
              ? 'linear-gradient(to bottom, #252523 0%, #000000 50%)'
              : 'linear-gradient(to bottom, #d6d7d5 0%, #f4f5f5 50%)',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
          }}
        >
          <Box
            sx={{
              width: '100%',
              padding: '0px 14px',
              display: 'flex',
            }}
          >
            <AppAudioLibraryoPlayer lesson={selectedLesson} />
          </Box>
        </Box>
      )}
      <Box
        sx={{
          m: '0px 20px',
        }}
      >
        {selectedLesson?.resource_files?.length > 0 || selectedLesson?.media_transcript ? (
          <>
            <Tabs
              sx={{
                margin: '24px 0px',
                height: '40px',
              }}
              variant="fullWidth"
              value={tabValue}
              indicatorColor="primary"
              onChange={handleChange}
              centered
            >
              <Tab label="Description" value="Description" />
              {selectedLesson?.resource_files?.length > 0 && <Tab label="Resources" value="Resources" />}
              {selectedLesson?.media_transcript && <Tab label="Transcript" value="Transcript" />}
            </Tabs>
            {tabValue === 'Description' && lessonText}
            {tabValue === 'Resources' && selectedLesson?.resource_files?.length > 0 && (
              <Box>
                <FileAttachments
                  files={selectedLesson?.resource_files}
                  onDelete={() => {}}
                  showDelete={false}
                  showEdit={false}
                />
              </Box>
            )}
            {tabValue === 'Transcript' && selectedLesson?.media_transcript && (
              <Box sx={{ bg: 'white' }}>
                <Typography
                  sx={{
                    fontFamily: 'Graphik Regular',
                    fontSize: '16px',
                    lineHeight: '28px',
                    marginTop: '20px',
                    color: theme.palette.text.secondary,
                  }}
                >
                  {formatTranscript(selectedLesson.media_transcript)}
                </Typography>
              </Box>
            )}
          </>
        ) : (
          lessonText
        )}
      </Box>
    </Box>
  )
}

const isLinkBold = (domNode) => {
  if (!domNode) return
  return domNode?.children?.[0]?.name === 'strong'
}

function LessonDisplay({ selectedLesson, handleMarkLesson, isMarkAsDoneBtnDisabled, lessonText }) {
  const [currentSection, setCurrentSection] = useState(null)
  const [expanded, setExpanded] = useState(false)
  const { theme, isDarkTheme } = useAppTheme()
  const [windowWidth, setWindowWidth] = useState(window.innerWidth)

  useEffect(() => {
    const handleResize = () => {
      setWindowWidth(window.innerWidth)
    }

    window.addEventListener('resize', handleResize)

    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [windowWidth])

  return (
    <Box sx={{ overflowX: 'hidden', overflowY: 'scroll', height: '100%' }}>
      <Box>
        {selectedLesson.type === 'video' && selectedLesson.media_file && (
          <Box
            sx={{
              height:
                windowWidth >= 1480
                  ? '600px'
                  : selectedLesson.media_file?.tracks?.[1]?.max_height <= 1080
                    ? 'auto'
                    : '600px',
              bg: '#f0f0f0',
            }}
          >
            <AppMuxPlayer
              autoPlay={false}
              asset={selectedLesson.media_file}
              wrapperStyle={{ width: '100%', height: '100%' }}
              playerStyle={{
                width: '100%',
                height: '100%',
                '--media-object-fit': 'contain',
                '--pip-button': 'none',
                aspectRatio: '16 / 9',
                backgroundColor: '#000000',
              }}
              onEnded={(e) => {}}
              streamType="on-demand"
              poster={selectedLesson.thumbnail_url}
            />
          </Box>
        )}
        {selectedLesson.type === 'audio' && (
          <Box
            sx={{
              height: '400px',
              background:
                theme.palette.mode === 'dark'
                  ? 'linear-gradient(to bottom, #252523 0%, #000000 50%)'
                  : 'linear-gradient(to bottom, #d6d7d5 0%, #f4f5f5 50%)',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            <Box
              sx={{
                width: '696px',
              }}
            >
              <AppAudioLibraryoPlayer lesson={selectedLesson} />
            </Box>
          </Box>
        )}
      </Box>
      <Box sx={{ position: 'relative' }}>
        <Box sx={{ position: 'absolute', right: '24px', top: '24px' }}>
          <Button
            onClick={handleMarkLesson}
            disabled={isMarkAsDoneBtnDisabled}
            sx={{
              borderRadius: '10px',
              backgroundColor: isDarkTheme ? 'rgba(255, 255, 255, 0.08)' : '#F3F5F5',
              boxShadow: 'none',
              color: selectedLesson.done ? '#AEE78B' : theme.palette.text.primary,
              '&:hover': {
                backgroundColor: isDarkTheme ? '#434343' : '#43434350',
                boxShadow: 'none',
              },
            }}
          >
            {selectedLesson.done ? 'Done' : 'Mark as Done'}
          </Button>
        </Box>
        <Box
          sx={{
            maxWidth: 696,
            display: 'flex',
            flexDirection: 'column',
            mx: 'auto',
            padding: '80px 0px',
          }}
        >
          <Box sx={{ mb: '12px' }}>
            <Typography
              sx={{
                fontFamily: 'Graphik Medium',
                fontSize: '12px',
                lineHeight: '16px',
                color: isDarkTheme ? 'rgb(141, 148, 163)' : '#585D66',
              }}
            >
              {currentSection?.name}
            </Typography>
          </Box>
          <Box sx={{ mb: '16px' }}>
            <Typography
              sx={{
                fontFamily: 'Graphik Semibold',
                fontSize: '24px',
                lineHeight: '32px',
                color: theme.palette.text.primary,
              }}
            >
              {selectedLesson.title}
            </Typography>
          </Box>
          {lessonText}
          {selectedLesson?.resource_files?.length > 0 && (
            <>
              <Divider sx={{ borderColor: 'rgba(141, 148, 163, 0.16)', borderWidth: '1px', mb: '32px' }} />
              <Box sx={{ mt: 2, mb: 4 }}>
                <Typography
                  sx={{
                    fontFamily: 'Graphik Semibold',
                    fontSize: '16px',
                    lineHeight: '20px',
                    color: theme.palette.text.primary,
                    mb: '16px',
                  }}
                >
                  Resources
                </Typography>
                <FileAttachments
                  files={selectedLesson?.resource_files}
                  onDelete={() => {}}
                  showDelete={false}
                  showEdit={false}
                />
              </Box>
            </>
          )}
          {selectedLesson.type === 'video' &&
            selectedLesson.media_transcript &&
            selectedLesson.media_transcript !== VIDEO_TRANSCRIPTION_GENERAL_ERROR_MSG &&
            selectedLesson.media_transcript !== VIDEO_TRANSCRIPTION_NO_SOUND_ERROR_MSG && (
              <Box
                sx={{
                  mb: '48px',

                  backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
                  '& .MuiAccordionSummary-expandIconWrapper': {
                    position: 'absolute',
                    left: '0px',
                    top: '40px',
                    color: theme.palette.text.disabled,
                  },
                }}
              >
                <Divider sx={{ borderColor: 'rgba(141, 148, 163, 0.16)', borderWidth: '1px', mb: '32px' }} />
                <Accordion
                  expanded={expanded}
                  onChange={(event, isExpanded) => setExpanded(isExpanded)}
                  sx={{
                    backgroundColor: 'inherit',
                    '& .MuiCollapse-wrapperInner': {
                      backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
                    },
                  }}
                >
                  <AccordionSummary
                    expandIcon={
                      <Typography
                        sx={{
                          fontFamily: 'Graphik Regular',
                          color: 'rgb(141, 148, 163)',
                          fontSize: '13px',
                        }}
                      >
                        {expanded ? 'Hide Transcript' : 'Show Transcript'}
                      </Typography>
                    }
                    aria-controls="panel1a-content"
                    id="panel1a-header"
                    sx={{
                      backgroundColor: 'inherit',
                      '& .MuiAccordionSummary-expandIconWrapper.Mui-expanded': {
                        transform: 'none',
                      },
                    }}
                  >
                    <Typography variant="h6" sx={{ mb: '16px', fontFamily: 'Graphik Semibold' }}>
                      Transcript
                    </Typography>
                  </AccordionSummary>
                  <AccordionDetails
                    sx={{
                      backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
                    }}
                  >
                    <Typography
                      sx={{
                        fontFamily: 'Graphik Regular',
                        fontSize: '16px',
                        lineHeight: '28px',
                        marginTop: '40px',
                        color: theme.palette.text.secondary,
                        backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
                      }}
                    >
                      {formatTranscript(selectedLesson.media_transcript)}
                    </Typography>
                  </AccordionDetails>
                </Accordion>
              </Box>
            )}
        </Box>
      </Box>
    </Box>
  )
}

export default function CoursePage() {
  const dispatch = useAppDispatch()
  const { theme } = useAppTheme()
  const isDarkTheme = useStore((state) => state.ui.isDarkTheme)
  const user = useAppSelector((state) => selectUser(state))
  const userProfile = useAppSelector((state) => selectUserProfile(state))
  const { contentLibrary: library } = useAppSelector((state) => selectContentLibrary(state))
  const pathname = usePathname()
  const params = useParams()
  const courseId = params.courseId
  const searchParams = useSearchParams()
  const lessonId = searchParams.get('lesson_id')
  const router = useRouter()
  const [selectedSectionId, setSelectedSectionId] = useState(null)
  const [selectedLessonId, setSelectedLessonId] = useState(null)
  /* const [open, setOpen] = useState(false) */
  const [openSectionIndex, setOpenSectionIndex] = useState([])
  const [courseSettings, setCourseSettings] = useState(null)
  /*   const [isRestarting, setIsRestarting] = useState(false)
   */
  const [isLoading, setIsLoading] = useState(false)
  const [sections, setSections] = useState({})
  const [isMarkAsDoneBtnDisabled, setIsMarkAsDoneBtnDisabled] = useState(false)
  const selectedLesson = sections[selectedSectionId]?.ContentLibraryCourseLesson[selectedLessonId]
  const [lessonOrder, setLessonOrder] = useState([])
  const membership = useAppSelector((state) => selectMembership(state))
  const { isCurrentUserAdmin } = useCheckUserRole()

  useEffect(() => {
    dispatch(getContentLibrary(membership.id))
  }, [])

  useEffect(() => {
    if (library?.id) {
      fetchCourseData(library.id, courseId)
    }
  }, [library?.id, courseId])

  const fetchCourseData = async (contentLibraryId) => {
    try {
      setIsLoading(true)
      const response = await getContentLibraryCourseApi(contentLibraryId, courseId, membership.slug)
      const courseData = response.data

      setCourseSettings(courseData)
      if (courseData?.ContentLibraryCourseSection) {
        const filteredVisibleSections = {}
        if (courseData.visibility === 'draft' && !isCurrentUserAdmin) {
          router.push('/library')
        }

        for (let sectionId in courseData.ContentLibraryCourseSection) {
          const section = courseData.ContentLibraryCourseSection[sectionId]
          /* creator should be able to see the lesson it's trying to preview */
          const creatorIsPreviewing = isCurrentUserAdmin && section.ContentLibraryCourseLesson[lessonId]
          if (section.visibility == 'published' || creatorIsPreviewing) {
            /* loop ContentLibraryCourseLesson object within section to filter the draft ones*/
            const filteredVisibleLessons = {}
            for (let lessonId in section.ContentLibraryCourseLesson) {
              const lesson = section.ContentLibraryCourseLesson[lessonId]

              if (lesson.visibility == 'published' || creatorIsPreviewing) {
                filteredVisibleLessons[lessonId] = lesson
              }
            }
            if (Object.keys(filteredVisibleLessons).length > 0) {
              section.ContentLibraryCourseLesson = filteredVisibleLessons

              filteredVisibleSections[sectionId] = section
            }
          }
        }
        courseData.ContentLibraryCourseSection = filteredVisibleSections

        let lessonOrderArr = []

        let orderedLessonIds = Object.values(courseData.ContentLibraryCourseSection)
          .sort((a: any, b: any) => a.sequence - b.sequence)
          .map((section: any) => {
            return {
              ...section,
              ContentLibraryCourseLesson: Object.values(section.ContentLibraryCourseLesson).sort(
                (a: any, b: any) => a.sequence - b.sequence,
              ),
            }
          })
          .forEach((section) => {
            section.ContentLibraryCourseLesson.forEach((lesson) => {
              lessonOrderArr.push({ sectionId: section.id, lessonId: lesson.id })
            })
          })

        setLessonOrder(lessonOrderArr)

        setCourseDataState(courseData)

        // Find the section and lesson index of the lesson with the id from router.query.lesson_id
        const queryParamLessonId = lessonId
        if (queryParamLessonId) {
          for (let sectionId in courseData.ContentLibraryCourseSection) {
            const section = courseData.ContentLibraryCourseSection[sectionId]
            for (let lessonId in section.ContentLibraryCourseLesson) {
              const lesson = section.ContentLibraryCourseLesson[lessonId]

              if (lesson.id === queryParamLessonId) {
                // Found the lesson, set the selected section and lesson index
                setSelectedSectionId(sectionId)
                setSelectedLessonId(lessonId)

                break
              }
            }
          }
        } else {
          /*  selects first section and first lesson that is not done, if every lesson is done select the last lesson*/
          for (let sectionId in courseData.ContentLibraryCourseSection) {
            const section = courseData.ContentLibraryCourseSection[sectionId]
            for (let lessonId in section.ContentLibraryCourseLesson) {
              const lesson = section.ContentLibraryCourseLesson[lessonId]
              if (!lesson.done) {
                console.log('not lesson done', lesson)
                setSelectedSectionId(sectionId)
                setSelectedLessonId(lessonId)
                return
              }
            }
          }
          // if it reached here it means all of the lessons are done, selecting the last one
          setSelectedSectionId(lessonOrderArr[lessonOrderArr.length - 1].sectionId)
          setSelectedLessonId(lessonOrderArr[lessonOrderArr.length - 1].lessonId)
        }
      }
      return courseData?.ContentLibraryCourseSection // Return the fetched data
    } catch (error) {
      // Handle the error here
      console.log('Failed to fetch course data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const setCourseDataState = (courseData) => {
    // Update the lesson statuses in the sections
    if (Array.isArray(courseData.lesson_statuses)) {
      courseData.lesson_statuses.forEach((status) => {
        const section = courseData.ContentLibraryCourseSection[status.section_id]
        if (section) {
          const lesson = section.ContentLibraryCourseLesson[status.lesson_id]
          if (lesson) {
            lesson.done = status.done
          }
        }
      })
    }

    setSections(courseData.ContentLibraryCourseSection)

    // Set the first uncompleted lesson of the first section as selected
    for (let sectionId in courseData.ContentLibraryCourseSection) {
      const section = courseData.ContentLibraryCourseSection[sectionId]
      for (let lessonId in section.ContentLibraryCourseLesson) {
        const lesson = section.ContentLibraryCourseLesson[lessonId]
        if (!lesson.done) {
          setSelectedSectionId(sectionId)
          setSelectedLessonId(lessonId)
          return
        }
      }
    }
  }
  /* const handleClose = () => {
    setOpen(false)
  }
 */
  const handleBackButtonClick = () => {
    if (isCurrentUserAdmin) {
      /* wait 1 second */
      setTimeout(() => {
        router.push(`/${membership.slug}/content/course-builder/${courseId}?lesson_id=${lessonId}`)
      }, 1500)
    } else {
      router.push(`/${membership.slug}/content`)
    }
  }

  const handleLessonClick = async (sectionId, lessonId) => {
    if (isMarkAsDoneBtnDisabled) {
      return
    }

    setSelectedSectionId(sectionId)
    setSelectedLessonId(lessonId)

    //update query param lesson_id
    router.push(`${pathname}?lesson_id=${lessonId}`)

    if (isMobile) {
      setHidenSidebar(true)
    }
  }

  const calculateCompletionPercentage = () => {
    const sectionsArray = Object.values(sections)
    const totalLessons: number = sectionsArray.reduce(
      (total: any, section: any) => total + Object.values(section.ContentLibraryCourseLesson).length,
      0,
    ) as number

    const completedLessonsCount: number = sectionsArray.reduce(
      (total: any, section: any) =>
        total + Object.values(section.ContentLibraryCourseLesson).filter((lesson: any) => lesson.done).length,
      0,
    ) as number

    const completionPercentage = ((completedLessonsCount as any) / totalLessons) * 100
    return roundCourseProgress(completionPercentage || 0)
  }

  const handleRestartCourse = async () => {
    try {
      /*  setIsRestarting(true) */
      await restartCourseApi(library.id, courseId as string)
      // Go to the first lesson of the first section
      setSelectedSectionId(sections[Object.keys(sections)[0]].id)
      setSelectedLessonId(sections[Object.keys(sections)[0]].ContentLibraryCourseLesson[0].id)

      setSections((prevSections) => {
        const newSections = { ...prevSections }
        for (let sectionId in newSections) {
          const section = newSections[sectionId]
          for (let lessonId in section.ContentLibraryCourseLesson) {
            const lesson = section.ContentLibraryCourseLesson[lessonId]
            lesson.done = false
          }
        }
        return newSections
      })

      /* setOpen(false) */
    } catch (error) {
      console.error('Failed to restart course', error)
    } finally {
      /*  setIsRestarting(false) */
    }
  }

  const areAllLessonsCompleted = (section) => {
    return section.ContentLibraryCourseLesson.every((lesson) => lesson.done)
  }

  const handleSectionClick = (sectionIndex) => {
    setOpenSectionIndex((prevOpenSectionIndex) => {
      if (prevOpenSectionIndex.includes(sectionIndex)) {
        return prevOpenSectionIndex.filter((index) => index !== sectionIndex)
      } else {
        return [...prevOpenSectionIndex, sectionIndex]
      }
    })
  }

  const navigateToNextIncompleteLesson = () => {
    // Find the index of the current lesson in the lesson order array
    const currentLessonIndex = lessonOrder.findIndex((lesson) => lesson.lessonId === selectedLessonId)

    // Find the next incomplete lesson starting from the current lesson index
    const nextIncompleteLessonIndex = lessonOrder.findIndex((lesson, index) => {
      if (index <= currentLessonIndex) return false // Skip lessons up to and including the current lesson
      const section = sections[lesson.sectionId]
      const lessonDetail = section.ContentLibraryCourseLesson[lesson.lessonId]
      return !lessonDetail.done // Return true if the lesson is not done
    })

    // If there is a next incomplete lesson, update the selected section and lesson IDs
    if (nextIncompleteLessonIndex !== -1) {
      const nextLesson = lessonOrder[nextIncompleteLessonIndex]
      setSelectedSectionId(nextLesson.sectionId)
      setSelectedLessonId(nextLesson.lessonId)
    }
  }

  const handleMarkLesson = async () => {
    setIsMarkAsDoneBtnDisabled(true)
    const lessonDoneStatus = sections[selectedSectionId].ContentLibraryCourseLesson[selectedLessonId].done
    console.log('lessonDoneStatus', lessonDoneStatus)
    // Optimistically update the states
    setSections((prevSections) => {
      const newSections = { ...prevSections }
      const section = newSections[selectedSectionId]
      const lesson = section.ContentLibraryCourseLesson[selectedLessonId]
      lesson.done = !lesson.done
      return newSections
    })

    // If the lesson is marked as done, navigate to the next incomplete lesson
    try {
      await updateUserLessonStatusApi(library.id, courseId as string, selectedLessonId, {
        done: !lessonDoneStatus,
      })

      // If the lesson is marked as done, navigate to the next incomplete lesson
      if (!lessonDoneStatus) {
        navigateToNextIncompleteLesson()
      }
    } catch (error) {
      console.error(error)

      // Revert the optimistic update if the API request fails
      revertOptimisticUpdates(lessonDoneStatus)
    } finally {
      setIsMarkAsDoneBtnDisabled(false)
    }
  }

  const navigateToNextLesson = () => {
    // Convert sections and lessons to arrays and sort them by sequence
    const currentLessonIndex = lessonOrder.findIndex((lesson) => lesson.lessonId === selectedLessonId)
    const nextLessonIndex = currentLessonIndex + 1
    setSelectedSectionId(lessonOrder[nextLessonIndex].sectionId)
    setSelectedLessonId(lessonOrder[nextLessonIndex].lessonId)
  }

  const revertOptimisticUpdates = (currentMarkStatus) => {
    setSections((prevSections) => {
      const newSections = { ...prevSections }
      const section = newSections[selectedSectionId]
      const lesson = section.ContentLibraryCourseLesson[selectedLessonId]
      lesson.done = !currentMarkStatus
      return newSections
    })
  }

  const isMobile = useMediaQuery(theme.breakpoints.down('sm'))
  const [hidenSidebar, setHidenSidebar] = useState(isMobile)
  const [isBannerVisible, setIsBannerVisible] = useState(true)
  useEffect(() => {
    setHidenSidebar(isMobile)
  }, [isMobile])

  const lessonText = (
    <div className="lesson-text break-words font-sans text-base leading-7 text-pure-black dark:text-pure-white [&_strong]:font-semibold [&_ul]:pl-6 [&_ul_li]:list-disc">
      {parse(selectedLesson?.text || '')}
    </div>
  )

  if (!library) {
    return null
  }

  return (
    <div className="course-member-view-wrapper h-100 w-100 absolute left-0 top-0">
      {/* Preview Banner */}
      {isCurrentUserAdmin && !isMobile && (
        <>
          <Box
            sx={{
              top: 0,
              left: 0,
              zIndex: 999,
              width: '100%',
              height: isBannerVisible ? '59px' : '0px',
              display: 'flex',
              alignItems: 'center',
              position: 'absolute',
              justifyContent: 'space-between', // This will push the Button to the far right
              padding: '0px 10px 0px 20px',
              backgroundColor: '#17171a',
              overflow: 'hidden',
              transition: 'height 0.5s',
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <SVGEye styles={{ color: '#aee78b' }} />
              <Typography
                sx={{
                  fontFamily: 'Graphik Medium',
                  fontSize: '14px',
                  color: 'rgba(255, 255, 255, 0.87)',
                  marginLeft: '8px', // Optional: Add some space between the icon and the text
                }}
              >
                You are currently in Preview Mode
              </Typography>
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', marginRight: '50px' }}>
              <Button
                onClick={() =>
                  router.push(`/${membership.slug}/content/course-builder/${courseId}?lesson_id=${selectedLesson?.id}`)
                }
                variant="contained"
                color="primary"
                sx={{
                  borderRadius: '10px',
                  height: '30px',
                  width: '120px',
                  fontSize: '14px',
                  marginRight: '8px',
                  color: '#fff',
                }}
              >
                Edit Lesson
              </Button>
            </Box>
          </Box>
          <Box
            sx={{
              position: 'absolute',
              top: 10,
              right: 5,
              zIndex: 999,
              width: '40px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              transition: 'height 0.5s',
              overflow: 'hidden',
            }}
          >
            <IconButton
              sx={{
                color: theme.palette.text.primary,
                position: 'relative',
                backgroundColor: '#29292c',
              }}
              size="medium"
              onClick={() => setIsBannerVisible(!isBannerVisible)}
            >
              <ArrowLeft
                sx={{
                  transform: isBannerVisible ? 'rotate(90deg)' : 'rotate(270deg)',
                  transition: 'transform 0.3s ease-in-out',
                  color: '#fff',
                }}
              />
            </IconButton>
          </Box>
        </>
      )}
      <Box
        className="w-100 h-100"
        sx={{
          display: 'flex',
          marginTop: isBannerVisible && isCurrentUserAdmin && !isMobile ? '59px' : '0px',
          paddingBottom: isBannerVisible && isCurrentUserAdmin && !isMobile ? '59px' : '0px',
          transition: '0.5s cubic-bezier(.36,-0.01,0,.77)',
        }}
      >
        {/* Left Column */}
        <Box
          onTransitionEnd={() => {}}
          sx={{
            height: '100%',
            width: hidenSidebar ? '0px' : isMobile ? '90%' : '316px',
            minWidth: hidenSidebar ? '0px' : '316px',
            backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#f3f5f5',
            overflowX: 'hidden',
            whiteSpace: 'nowrap',
            display: 'flex',
            flexDirection: 'column',
            zIndex: 1,
            scrollbarWidth: 'none',
            '&::-webkit-scrollbar': {
              display: 'none',
            },
          }}
        >
          <CourseNavigator
            courseSettings={courseSettings}
            sections={sections}
            user={user}
            userProfile={userProfile}
            selectedSectionId={selectedSectionId}
            selectedLessonId={selectedLessonId}
            handleBackButtonClick={handleBackButtonClick}
            calculateCompletionPercentage={calculateCompletionPercentage}
            handleSectionClick={handleSectionClick}
            handleLessonClick={handleLessonClick}
            areAllLessonsCompleted={areAllLessonsCompleted}
            openSectionIndex={openSectionIndex}
            isLoading={isLoading}
            isMarkAsDoneBtnDisabled={isMarkAsDoneBtnDisabled}
          />
        </Box>

        {/* Right Column */}
        <Box
          sx={{
            width: hidenSidebar ? '100%' : isMobile ? '100%' : 'calc(100% - 316px)',
            height: '100%',
            // hide overflow scrollbar
            '&::-webkit-scrollbar': {
              display: isMobile && 'none',
            },
            transition: '0.5s cubic-bezier(.36,-0.01,0,.77)',
            left: '0px',
            position: isMobile && 'absolute',
            zIndex: 0,
            //backgroundColor: 'rgb(33, 3, 36)',
            backgroundColor: isDarkTheme ? '#212124' : '#ffffff',
            opacity: isMobile && !hidenSidebar && 0.5,
          }}
        >
          {selectedLesson &&
            (isMobile ? (
              <Box
                sx={{
                  pointerEvents: !hidenSidebar && 'none',
                }}
              >
                <LessonDisplayMobile
                  selectedLesson={selectedLesson}
                  handleMarkLesson={handleMarkLesson}
                  setHidenSidebar={setHidenSidebar}
                  navigateToNextLesson={navigateToNextLesson}
                  disableNextBtn={courseSettings.last_lesson_id_in_course_member_view === selectedLesson.id}
                  lessonText={lessonText}
                />
              </Box>
            ) : (
              <>
                <LessonDisplay
                  selectedLesson={selectedLesson}
                  handleMarkLesson={handleMarkLesson}
                  isMarkAsDoneBtnDisabled={isMarkAsDoneBtnDisabled}
                  lessonText={lessonText}
                />
              </>
            ))}
        </Box>
        {!hidenSidebar && isMobile && (
          <IconButton
            onClick={() => setHidenSidebar(true)}
            aria-label="close sidebar"
            data-cy="close sidebar"
            size="large"
            sx={{
              position: 'absolute',
              top: '10px',
              right: '7px',
              backgroundColor: isDarkTheme ? 'rgb(23, 23, 26)' : '#F3F5F5',
              height: '40px',
              width: '40px',
              color: theme.palette.text.secondary,
              zIndex: 1,
            }}
          >
            <SVGClose fontSize={14} />
          </IconButton>
        )}
        {/*  <RestartCourseDialog
          open={open}
          handleClose={handleClose}
          handleConfirmRestart={handleRestartCourse}
          isRestarting={isRestarting}
        /> */}
      </Box>
    </div>
  )
}
