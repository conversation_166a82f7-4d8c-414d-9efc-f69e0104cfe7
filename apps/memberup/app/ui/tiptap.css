.tiptap {
  outline: none;

  :first-child {
    margin-top: 0;
  }

  /* List styles */
  ul,
  ol {
    padding: 0 1rem;
    margin: 1.25rem 1rem 1.25rem 0.4rem;

    li p {
      margin-top: 0.25em;
      margin-bottom: 0.25em;
    }
  }

  /* Heading styles */
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    line-height: 1.1;
    margin-top: 2.5rem;
    text-wrap: pretty;
  }

  h1,
  h2 {
    margin-top: 3.5rem;
    margin-bottom: 1.5rem;
  }

  h1 {
    font-size: 1.4rem;
  }

  h2 {
    font-size: 1.2rem;
  }

  h3 {
    font-size: 1.1rem;
  }

  h4,
  h5,
  h6 {
    font-size: 1rem;
  }

  a {
    color: hsl(var(--community-color-primary));
    text-decoration: underline;
  }

  ul {
    @apply list-disc;
  }

  ol {
    @apply list-decimal;
  }

  /* Code and preformatted text styles */
  code {
    background-color: var(--purple-light);
    border-radius: 0.4rem;
    color: var(--black);
    font-size: 0.85rem;
    padding: 0.25em 0.3em;
  }

  pre {
    background: var(--black);
    border-radius: 0.5rem;
    color: var(--white);
    font-family: 'JetBrainsMono', monospace;
    margin: 1.5rem 0;
    padding: 0.75rem 1rem;

    code {
      background: none;
      color: inherit;
      font-size: 0.8rem;
      padding: 0;
    }
  }

  blockquote {
    border-left: 3px solid var(--gray-3);
    margin: 1.5rem 0;
    padding-left: 1rem;
  }

  hr {
    border: none;
    border-top: 1px solid var(--gray-2);
    margin: 2rem 0;
  }
}

/* Bubble menu */
.bubble-menu {
  @apply bg-red-200;
  border: 1px solid var(--gray-1);
  border-radius: 0.4375rem;
  box-shadow: var(--shadow);
  display: flex;
  padding: 0.25rem;
  display: flex;
  align-items: center;
  justify-content: center;

  button {
    @apply bg-white-500;
    width: 1.5rem;
    height: 1.5rem;
    margin-right: 0.25rem;
    border-radius: 0.3125rem;

    &:last-child {
      margin-right: 0;
    }

    &:not(:disabled):hover {
      background-color: var(--gray-3);
    }

    &:disabled {
      @apply opacity-50;
      @apply cursor-not-allowed;
    }

    &.active {
      @apply bg-grey-300;
    }
  }
}

body.dark .bubble-menu {
  @apply bg-black-700;

  button {
    display: flex;
    align-items: center;
    justify-content: center;
    @apply transition-colors;
    @apply text-black-100;
    @apply bg-transparent;

    &:not(:disabled):not(.active):hover {
      @apply bg-black-500;
    }

    &:disabled:hover {
      @apply bg-transparent;
    }

    &.active {
      @apply bg-grey-800;
    }
  }
}
