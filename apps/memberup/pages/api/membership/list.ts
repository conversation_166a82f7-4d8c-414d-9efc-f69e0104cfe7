import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHandler } from '@memberup/shared/src/libs/prisma/error-handler'
import { findMemberships } from '@memberup/shared/src/libs/prisma/membership'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.get(async (req, res) => {
  try {
    // TODO 3023: Pass filters and just search for discoverable communities.
    const memberships = await findMemberships({
      where: {
        membership_setting: {
          discoverable: true,
        },
      },
      include: {
        membership_setting: true,
      },
    })

    return res.json({
      success: true,
      data: {
        memberships,
      },
    })
  } catch (err: any) {
    console.error('membership err ==== ', err)
    res.status(500).json(errorHandler(err, 'Membership'))
  }
})

export default handler
