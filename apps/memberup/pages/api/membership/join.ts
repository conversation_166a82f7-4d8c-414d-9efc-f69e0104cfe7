import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { USER_ROLE_ENUM } from '@memberup/shared/src/types/enum'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import { findCommunityById } from '@/shared-libs/prisma/membership'
import prisma from '@/shared-libs/prisma/prisma'
import { findFirstUserMembership } from '@/shared-libs/prisma/user-membership'
import { USER_MEMBERSHIP_STATUS_ENUM as STATUS } from '@/shared-types/enum'
import { IMembershipSetting } from '@/shared-types/interfaces'

const handler = nc<NextApiRequest, NextApiResponse>()

const computeIsPaidCommunity = (membershipSetting: IMembershipSetting) => {
  return (
    membershipSetting?.stripe_prices?.some((price) => price.active) &&
    membershipSetting.stripe_connect_account?.stripe_user_id
  )
}

/*
    Creates or update the user membership setting to pending for free communities or payment_pending otherwise.
 */

const INVITE_TOKEN_SECRET = process.env.INVITE_TOKEN_SECRET

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const user = req['user']
    const { membership_id: membershipId, form_data: formData, invite_token } = req.body

    // Get the membership or fail
    const membership = await findCommunityById(membershipId)
    if (!membership) {
      return status400(res, 'Membership not found')
    }

    const userMembership = await findFirstUserMembership({ user_id: user.id, membership_id: membershipId })
    // Fail if user is already part of the community
    if (userMembership?.status === STATUS.accepted) {
      return status400(res, 'You are already a member of this community.')
    }

    // Fail if user has been banned or rejected.
    if (userMembership?.status === STATUS.banned || userMembership?.status === STATUS.rejected) {
      return status400(res, 'You are not allowed to join this community.')
    }

    // Create or update the user membership
    const inviteToken = invite_token
    if (inviteToken) {
      const decodedToken = jwt.verify(inviteToken, INVITE_TOKEN_SECRET)
      if (decodedToken) {
        const inviteLinkResult = await prisma.inviteLink.findFirst({
          where: {
            token: inviteToken,
            active: true,
          },
        })

        if (inviteLinkResult) {
          const createdUserMembership = await prisma.userMembership.create({
            data: {
              user_id: req['user'].id,
              membership_id: membershipId,
              user_role: inviteLinkResult.role,
              status: STATUS.pending,
              user_answer: typeof formData === 'object' && Object.keys(formData).length > 0 ? formData : null,
            },
            include: {
              membership: true,
            },
          })

          await prisma.inviteLink.update({
            where: {
              id: inviteLinkResult.id,
            },
            data: {
              active: false,
            },
          })
          return status200(res, createdUserMembership)
        }
      }
    }

    const isPaidCommunity = Boolean(membership.membership_setting.is_pricing_enabled)
    let membershipStatus: STATUS = isPaidCommunity ? STATUS.payment_pending : STATUS.pending
    if (userMembership) {
      const updatedUserMembership = await prisma.userMembership.update({
        where: {
          id: userMembership.id,
        },
        data: {
          status: membershipStatus,
          user_answer: typeof formData === 'object' && Object.keys(formData).length > 0 ? formData : null,
        },
        include: {
          membership: true,
        },
      })
      return status200(res, updatedUserMembership)
    } else {
      const createdUserMembership = await prisma.userMembership.create({
        data: {
          user_id: req['user'].id,
          membership_id: membershipId,
          user_role: USER_ROLE_ENUM.member,
          status: membershipStatus,
          user_answer: typeof formData === 'object' && Object.keys(formData).length > 0 ? formData : null,
        },
        include: {
          membership: true,
        },
      })
      return status200(res, createdUserMembership)
    }
  } catch (e) {
    console.error(e)
    return status500(res, e.message)
  }
})

export default handler
