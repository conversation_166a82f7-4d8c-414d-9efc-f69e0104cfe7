import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { updateAlgoliaMembersIndexForUserId } from '@/shared-libs/algolia'
import prisma from '@/shared-libs/prisma/prisma'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'
import authenticationMiddleware from '@/src/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).post(async (req, res) => {
  const { id } = req.query

  const userMembership = await prisma.userMembership.findUnique({
    where: {
      id: id as string,
    },
  })

  if (!userMembership) {
    return res.status(400).json({
      success: false,
      message: 'User Membership not found',
    })
  }

  const updatedUserMembership = await prisma.userMembership.update({
    where: {
      id: userMembership.id,
    },
    data: {
      status: USER_MEMBERSHIP_STATUS_ENUM.accepted,
    },
  })
  try {
    await updateAlgoliaMembersIndexForUserId(userMembership.user_id)
  } catch (error) {
    console.error('Failed to update Algolia members index:', error)
    // Index update failed but membership was approved successfully
  }
  return res.status(200).json({ data: updatedUserMembership })
})
export default handler
