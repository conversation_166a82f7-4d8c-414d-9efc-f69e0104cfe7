import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import prisma from '@/shared-libs/prisma/prisma'
import { findUserMembershipAsAdminOrCreator } from '@/shared-libs/prisma/user-membership'
import { USER_MEMBERSHIP_STATUS_ENUM } from '@/shared-types/enum'
import authenticationMiddleware from '@/src/middlewares/authentication'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const { membership_id } = req.query
    const user = req['user']

    // Verify that the user requesting is the owner or admin.
    const userMembership = await findUserMembershipAsAdminOrCreator(user.id, membership_id as string)
    if (!userMembership) {
      return res.status(403).end()
    }

    const where = {
      membership_id: membership_id as string,
      status: USER_MEMBERSHIP_STATUS_ENUM.pending,
    }

    const userMemberships = await prisma.userMembership.findMany({
      where,
      include: {
        user: {
          include: {
            profile: true,
          },
        },
      },
    })
    return res.json({ success: true, data: userMemberships })
  } catch (e) {
    console.error(e)
    return res.status(500).json({
      success: false,
      message: e.message,
    })
  }
})
export default handler
