import * as Sentry from '@sentry/nextjs'
import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import {
  activeCampaignGetContactFieldValues,
  activeCampaignGetContacts,
  activeCampaignUpdateContactCustomFieldValue,
} from '@memberup/shared/src/libs/active-campaign'
import {
  knockAddObjects,
  knockDeleteObjectsAndSubscriptions,
  knockSetTenant,
  knockTriggerWorkflow,
} from '@memberup/shared/src/libs/knock'
import { findInviteLinks, updateInviteLink } from '@memberup/shared/src/libs/prisma/invite-link'
import { updateMembership } from '@memberup/shared/src/libs/prisma/membership'
import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { findUsers } from '@memberup/shared/src/libs/prisma/user'
import { KNOC<PERSON>_WORKFLOW_ENUM, USER_ROLE_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { membershipQueryOptions } from '@/lib/query-options/communities'
import { sanitizeHTML } from '@/lib/server/sanitization'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import checkCreatorRoleMiddleware from '@/memberup/middlewares/check-creator-role'
import {
  activeCampaignAddContact,
  activeCampaignAddContactToList,
  activeCampaignCreateContactCustomFieldValue,
  activeCampaignUpdateContact,
} from '@/shared-libs/active-campaign'
import { status200, status403 } from '@/shared-libs/api-utils'
import { KNOCK_OBJECT_IDS, KNOCK_OBJECTS, knockBulkAddSubscriptions } from '@/shared-libs/knock'
import prisma from '@/shared-libs/prisma/prisma'
import { findUserMembershipAsAdminOrCreator, findUserMembershipByIds } from '@/shared-libs/prisma/user-membership'
import { IMembership } from '@/shared-types/interfaces'

const DEFAULT_DOMAIN = process.env.NEXT_PUBLIC_DEFAULT_DOMAIN
const ACTIVE_CAMPAIGN_API_URL = process.env.ACTIVE_CAMPAIGN_API_URL
const ACTIVE_CAMPAIGN_API_KEY = process.env.ACTIVE_CAMPAIGN_API_KEY
const ACTIVE_CAMPAIGN_ENABLED = process.env.ACTIVE_CAMPAIGN_ENABLED === 'true'
const ACTIVE_CAMPAIGN_CONTACT_LIST_ID = process.env.ACTIVE_CAMPAIGN_CONTACT_LIST_ID
const NEXTAUTH_SECRET = process.env.NEXTAUTH_SECRET

export const addContactToActiveCampaign = async (email: string, firstName: string, lastName: string, host: string) => {
  if (ACTIVE_CAMPAIGN_ENABLED && ACTIVE_CAMPAIGN_API_URL && ACTIVE_CAMPAIGN_API_KEY) {
    const temp = await activeCampaignGetContacts(ACTIVE_CAMPAIGN_API_URL, ACTIVE_CAMPAIGN_API_KEY, email)
    let contactId = temp?.contacts?.[0]?.id

    if (contactId) {
      await activeCampaignUpdateContact(
        ACTIVE_CAMPAIGN_API_URL,
        ACTIVE_CAMPAIGN_API_KEY,
        contactId,
        email,
        firstName,
        lastName,
      )
    } else {
      const temp1 = await activeCampaignAddContact(
        ACTIVE_CAMPAIGN_API_URL,
        ACTIVE_CAMPAIGN_API_KEY,
        email,
        firstName,
        lastName,
        [],
      )
      contactId = temp1?.contact?.id
      if (contactId) {
        await activeCampaignAddContactToList(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          parseInt(`${ACTIVE_CAMPAIGN_CONTACT_LIST_ID}`),
          parseInt(`${contactId}`),
        )
      }
    }

    if (contactId) {
      const customFieldValues = await activeCampaignGetContactFieldValues(
        ACTIVE_CAMPAIGN_API_URL,
        ACTIVE_CAMPAIGN_API_KEY,
        contactId,
      )

      let membershipFieldValue = customFieldValues?.fieldValues?.find((item) => item.field === '12')
      if (membershipFieldValue) {
        activeCampaignUpdateContactCustomFieldValue(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          contactId,
          membershipFieldValue.field,
          membershipFieldValue.id,
          'Incomplete',
        )
      } else {
        activeCampaignCreateContactCustomFieldValue(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          contactId,
          '12',
          'Incomplete',
        )
      }

      membershipFieldValue = customFieldValues?.fieldValues?.find((item) => item.field === '4')
      if (membershipFieldValue) {
        activeCampaignUpdateContactCustomFieldValue(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          contactId,
          membershipFieldValue.field,
          membershipFieldValue.id,
          `https://${host}`,
        )
      } else {
        activeCampaignCreateContactCustomFieldValue(
          ACTIVE_CAMPAIGN_API_URL,
          ACTIVE_CAMPAIGN_API_KEY,
          contactId,
          '4',
          `https://${host}`,
        )
      }
    }
  }
}

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .use(checkCreatorRoleMiddleware)
  .put(async (req, res) => {
    try {
      const user = req['user']
      const { membership_id } = req.query

      // Verify if the user has the permission to update the membership and membership settings
      const userMembership = await findUserMembershipAsAdminOrCreator(user.id, membership_id as string)
      if (!userMembership) {
        return status403(res, `You don't have permissions to update the community.`)
      }

      const membership = userMembership.membership
      const membershipSetting = membership.membership_setting

      const oldMembershipName = membership.name
      const oldMembershipSlug = membership.slug
      const newAboutText = req.body.about_text
      const newAboutTitle = req.body.about_title
      const newAboutGallery = req.body.about_gallery
      const newMembershipName = req.body.name
      const newDescription = req.body.description
      const newMembershipSlug = req.body.slug
      const newVisibility = req.body.visibility
      const newCoverImage = req.body.cover_image
      const newCoverImageCropArea = req.body.cover_image_crop_area
      const newFavicon = req.body.favicon
      const newFaviconCropArea = req.body.favicon_crop_area
      const newExternalLinks = req.body.external_links
      const newSupportEmail = req.body.support_email
      const newThemeMainColor = req.body.theme_main_color
      const newForm = req.body.form
      const newFormEnabled = req.body.form_enabled

      let data = {}
      if (newMembershipName && newMembershipName !== oldMembershipName) {
        data['name'] = newMembershipName
      }
      if (newMembershipSlug && newMembershipSlug !== oldMembershipSlug) {
        data['slug'] = newMembershipSlug
      }

      // Update the membership
      let updatedMembership: IMembership
      let token: string

      if (Object.keys(data).length > 0) {
        updatedMembership = await updateMembership({
          where: { id: membership.id },
          data,
          include: {
            users: {
              where: {
                status: USER_STATUS_ENUM.active,
              },
            },
          },
        })

        const { users } = updatedMembership

        if (newMembershipName && newMembershipName !== oldMembershipName) {
          await knockSetTenant(updatedMembership.id, { name: updatedMembership.name })
        }

        // NOTE: If the membership slug changed we need to cleanup and recreate all the knock objects

        try {
          if (newMembershipSlug && newMembershipSlug !== oldMembershipSlug) {
            const userIds = users.map((item) => item.id)

            await knockDeleteObjectsAndSubscriptions(oldMembershipSlug, userIds)

            await knockAddObjects(newMembershipSlug, KNOCK_OBJECTS)
            await knockBulkAddSubscriptions(newMembershipSlug, KNOCK_OBJECT_IDS, userIds)

            const findUserResult = await findUsers({
              select: {
                id: true,
              },
              where: {
                membership_id: membership.id,
                role: { in: [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner] },
                status: USER_STATUS_ENUM.active,
              },
            })

            const recipientIds = findUserResult.docs.map((item) => item.id)
            if (recipientIds.length) {
              await knockTriggerWorkflow(
                membership.id,
                KNOCK_WORKFLOW_ENUM.community_name_changed,
                user.id,
                recipientIds,
                {
                  creator_name: user.first_name,
                  community_name: newMembershipName,
                  community_url: `https://${updatedMembership.slug}.${DEFAULT_DOMAIN}`,
                },
              )
            }

            const inviteLinks = await findInviteLinks({
              where: {
                membership_id: user.current_membership_id,
                url: {
                  contains: `://${oldMembershipSlug}.${DEFAULT_DOMAIN}`,
                },
              },
            })

            // Update all invite links to use the new slug as subdomain.
            for (const inviteLink of inviteLinks.docs) {
              await updateInviteLink({
                where: { id: inviteLink.id },
                data: {
                  url: inviteLink.url.replace(
                    `://${oldMembershipSlug}.${DEFAULT_DOMAIN}`,
                    `://${updatedMembership.slug}.${DEFAULT_DOMAIN}`,
                  ),
                },
              })
            }

            const owner = users?.find((item) => item.role === USER_ROLE_ENUM.owner)
            if (owner) {
              const temp = await activeCampaignGetContacts(
                ACTIVE_CAMPAIGN_API_URL,
                ACTIVE_CAMPAIGN_API_KEY,
                owner.email,
              )
              let contactId = temp?.contacts?.[0]?.id
              if (contactId) {
                const customFieldValues = await activeCampaignGetContactFieldValues(
                  ACTIVE_CAMPAIGN_API_URL,
                  ACTIVE_CAMPAIGN_API_KEY,
                  contactId,
                )

                const customFieldValue = customFieldValues?.fieldValues?.find((item) => item.field === '4')

                if (customFieldValue) {
                  activeCampaignUpdateContactCustomFieldValue(
                    ACTIVE_CAMPAIGN_API_URL,
                    ACTIVE_CAMPAIGN_API_KEY,
                    contactId,
                    customFieldValue.field,
                    customFieldValue.id,
                    `https://${updatedMembership.host}`,
                  )
                }
              }
            }
            token = jwt.sign(
              {
                email: user.email,
                //password: decodedToken.password,
                redirectTo: 'settings/community-settings',
              },
              NEXTAUTH_SECRET,
            )
          }
        } catch (e) {
          console.error(e)
        }
      }

      // Update the membership settings.
      data = {}
      if (newDescription !== membershipSetting.description) {
        data['description'] = newDescription
      }

      if (JSON.stringify(newExternalLinks) !== JSON.stringify(membershipSetting.external_links)) {
        data['external_links'] = newExternalLinks
      }

      if (newVisibility !== membershipSetting.visibility) {
        data['visibility'] = newVisibility
      }

      if (newFavicon !== membershipSetting.favicon) {
        data['favicon'] = newFavicon
      }

      if (newFaviconCropArea !== membershipSetting.favicon_crop_area) {
        data['favicon_crop_area'] = newFaviconCropArea
      }

      if (newCoverImage !== membershipSetting.cover_image) {
        data['cover_image'] = newCoverImage
      }

      if (newCoverImageCropArea !== membershipSetting.cover_image_crop_area) {
        data['cover_image_crop_area'] = newCoverImageCropArea
      }

      if (newSupportEmail !== membershipSetting.support_email) {
        data['support_email'] = newSupportEmail
      }

      if (newThemeMainColor !== membershipSetting.theme_main_color) {
        data['theme_main_color'] = newThemeMainColor
      }

      if (newAboutText && newAboutText !== membershipSetting.about_text) {
        data['about_text'] = sanitizeHTML(newAboutText, [
          'b',
          'i',
          'strong',
          'em',
          'u',
          'ol',
          'ul',
          'li',
          'a',
          'p',
          'br',
        ])
      }

      if (newAboutTitle !== membershipSetting.about_title) {
        data['about_title'] = newAboutTitle
      }

      if (newAboutGallery) {
        data['about_gallery'] = newAboutGallery
      }

      if (newForm) {
        data['form'] = newForm
      }

      if (newFormEnabled) {
        data['form_enabled'] = newFormEnabled
      }

      let updatedMembershipSettings = {}

      if (Object.keys(data).length > 0) {
        updatedMembershipSettings = await updateMembershipSetting({
          where: {
            id: membershipSetting.id,
          },
          data,
        })
      }

      const latestUserMembership = await findUserMembershipByIds(membership.id, user.id, 'accepted')

      if (updatedMembership) {
        // Ensure updatedMembership.membership_setting is updated
        updatedMembership.membership_setting = updatedMembershipSettings || membershipSetting
      }

      const latestMembership = (await prisma.membership.findUnique({
        where: { id: membership.id },
        ...membershipQueryOptions,
      })) as IMembership

      return status200(res, {
        token: token,
        membership: latestMembership,
        user_membership: latestUserMembership,
      })
    } catch (err: any) {
      console.error(err)
      Sentry.captureException(err)
      return res.status(500).end()
    }
  })

export default handler
