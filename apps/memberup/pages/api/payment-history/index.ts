import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { status200, status400, status500 } from '@/shared-libs/api-utils'
import { formatPaymentDescription } from '@/shared-libs/prisma/payments'
import { prisma } from '@/shared-libs/prisma/prisma'
import { PAYMENT_TYPE_ENUM } from '@/shared-types/enum'

const handler = nc<NextApiRequest, NextApiResponse>()

handler.use(authenticationMiddleware).get(async (req, res) => {
  try {
    const user = req['user']
    const { page = '1', pageSize = '10', membershipId } = req.query

    const pageNumber = parseInt(page as string, 10)
    const pageSizeNumber = parseInt(pageSize as string, 10)

    if (isNaN(pageNumber) || isNaN(pageSizeNumber) || pageNumber < 1 || pageSizeNumber < 1) {
      return status400(res, 'Invalid pagination parameters')
    }

    // Build the where clause based on the query parameters
    const where: any = {
      user_id: user.id,
    }

    if (membershipId) {
      where.membership_id = membershipId as string
    }

    // Get total count for pagination

    const totalCount = await prisma.payment.count({ where })

    // Calculate total pages
    const totalPages = Math.ceil(totalCount / pageSizeNumber)

    // Fetch payment history with pagination
    const paymentHistory = await prisma.payment.findMany({
      where,
      include: {
        membership: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
      },
      orderBy: {
        payment_date: 'desc',
      },
      skip: (pageNumber - 1) * pageSizeNumber,
      take: pageSizeNumber,
    })

    // Format the response
    const formattedPaymentHistory = paymentHistory.map((payment) => {
      // Use the stored description if available, otherwise generate a formatted one
      let description = payment.description || ''

      // If no description is stored, generate one with proper formatting
      if (!description) {
        if (payment.payment_type === PAYMENT_TYPE_ENUM.membership && payment.membership) {
          description = formatPaymentDescription(PAYMENT_TYPE_ENUM.membership, payment.membership.name)
        } else if (payment.payment_type === PAYMENT_TYPE_ENUM.memberup) {
          description = formatPaymentDescription(PAYMENT_TYPE_ENUM.memberup)
        } else {
          description = 'Payment'
        }
      }

      return {
        id: payment.id,
        amount: payment.amount,
        currency: payment.currency,
        description,
        payment_date: payment.payment_date,
        status: payment.status,
        invoice: payment.stripe_invoice,
        payment_type: payment.payment_type,
        stripe_receipt_url: payment.stripe_receipt_url,
        membership: payment.membership,
      }
    })

    return status200(res, {
      data: formattedPaymentHistory,
      pagination: {
        page: pageNumber,
        pageSize: pageSizeNumber,
        totalCount,
        totalPages,
      },
    })
  } catch (err: any) {
    console.error('Error fetching payment history:', err)
    return status500(res, err.message)
  }
})

export default handler
