import { serve } from 'inngest/next'

import innMembershipUpdateMembersCount from '@/shared-services/functions/membership.update-members-count'
import innMemberupFeedDeleted from '@/shared-services/functions/memberup.feed.deleted'
import innMemberUpSparkNextQuestion, {
  runSparkQuestionRotate,
} from '@/shared-services/functions/memberup.spark.next-question'
import innMuxVideoAssetStatus from '@/shared-services/functions/mux.video.asset.status'
import innStripeCustomerDeleted from '@/shared-services/functions/stripe.customer.deleted'
import innStripeCustomerSubscriptionDeleted from '@/shared-services/functions/stripe.customer.subscription.deleted'
import innStripeCustomerSubscriptionUpdated from '@/shared-services/functions/stripe.customer.subscription.updated'
import innStripeInvoicePaid from '@/shared-services/functions/stripe.invoice.paid'
import innStripePaymentIntentSucceeded from '@/shared-services/functions/stripe.payment_intent.succeeded'
import innStripePaymentMethodAttached from '@/shared-services/functions/stripe.payment_method.attached'
import { inngest } from '@/shared-services/inngest'

export default serve({
  client: inngest,
  functions: [
    runSparkQuestionRotate,
    innMemberUpSparkNextQuestion,
    innStripeCustomerDeleted,
    innStripeCustomerSubscriptionDeleted,
    innStripeCustomerSubscriptionUpdated,
    innStripePaymentIntentSucceeded,
    innStripePaymentMethodAttached,
    innStripeInvoicePaid,
    innMuxVideoAssetStatus,
    innMemberupFeedDeleted,
    innMembershipUpdateMembersCount,
  ],
})
