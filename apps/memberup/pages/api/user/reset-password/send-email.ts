import * as Sentry from '@sentry/nextjs'
import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { knockTriggerWorkflow } from '@memberup/shared/src/libs/knock'
import { findUser } from '@memberup/shared/src/libs/prisma/user'
import { KNOCK_WORKFLOW_ENUM, USER_STATUS_ENUM } from '@memberup/shared/src/types/enum'

const RESET_PASSWORD_TOKEN_SECRET = process.env.RESET_PASSWORD_TOKEN_SECRET

const handler = nc<NextApiRequest, NextApiResponse>()

handler.post(async (req, res) => {
  const { email } = req.body
  try {
    const user = await findUser({ where: { email } })

    if (!user) {
      // Always return success, even if the user doesn't exist
      return res.json({ success: true })
    }

    if (user.status === USER_STATUS_ENUM.banned) {
      return res.status(403).json({
        message: 'Your account has been banned from MemberUp platform.',
      })
    }

    const token = jwt.sign({ user_id: user.id }, RESET_PASSWORD_TOKEN_SECRET, { expiresIn: '72h' })

    await knockTriggerWorkflow(KNOCK_WORKFLOW_ENUM.reset_password, [user.id], {
      reset_link: `${req.headers.origin}/change-password?token=${token}`,
    })

    return res.json({
      success: true,
    })
  } catch (err) {
    console.error(err)
    Sentry.captureException(err)
    return res.status(500).end()
  }
})

export default handler
