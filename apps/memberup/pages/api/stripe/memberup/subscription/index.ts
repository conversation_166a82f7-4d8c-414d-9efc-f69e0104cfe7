// @ts-nocheck
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'
import Strip<PERSON> from 'stripe'

import { STRIPE_PRODUCT_ID_INSPIRED, STRIPE_PRODUCT_IDS_MAP, STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import { updateMembershipSetting } from '@memberup/shared/src/libs/prisma/membership-settings'
import { stripeGetSubscriptions } from '@memberup/shared/src/libs/stripe'
import { getAuthenticatedUserData } from '@/lib/server/users'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { STRIPE_PRODUCT_ID_INFINITE } from '@/shared-config/envs'
import { status400, status500 } from '@/shared-libs/api-utils'
import { findMembership } from '@/shared-libs/prisma/membership'
import { findUserMembership } from '@/shared-libs/prisma/user-membership'
import { stripeCreateSubscriptionMain, stripeGetPricesMain } from '@/shared-libs/stripe'
import { MEMBERUP_UNIFIED_ACCOUNT_PLANS } from '@/shared-settings/plans'

const handler = nc<NextApiRequest, NextApiResponse>()

handler
  .use(authenticationMiddleware)
  .get(async (req, res) => {
    try {
      const user = req['user']

      const stripeCustomerId = user.profile.stripe_customer_id
      if (!stripeCustomerId) {
        return res.status(400).json({
          message: `Membership didn't setup stripe. Please contact the owner.`,
        })
      }
      const result = await stripeGetSubscriptions(STRIPE_SECRET_KEY, {
        customer: stripeCustomerId,
      })
      return res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      res.status(500).json({ message: err.message })
    }
  })
  .post(async (req, res) => {
    try {
      const user = req['user']
      const { membership_id: membershipId, interval, plan_price, plan_key, promotion_code } = req.body
      const userMembership = await findUserMembership({
        where: { user_id: user.id, membership_id: membershipId },
        include: {
          membership: {
            include: {
              membership_setting: true,
            },
          },
        },
      })

      if (!userMembership) {
        return res.status(400).json({ message: `Invalid membership provided.` })
      }

      const membership = userMembership.membership
      const membershipSetting = membership.membership_setting

      const stripeCustomerId = user.profile.stripe_customer_id

      let result: Stripe.Response<Stripe.Subscription>

      const existingStripePrices = await stripeGetPricesMain(STRIPE_SECRET_KEY, {
        active: true,
        product: STRIPE_PRODUCT_ID_INFINITE,
        //lookup_keys: [plan_key],
        //recurring: { interval },
      })

      console.log(existingStripePrices, interval)

      if (!existingStripePrices) {
        return status500(res, `There are no active stripe price. Unexpected Error.`)
      }

      const stripePrice = plan_price
        ? existingStripePrices?.data?.find((p) => p.unit_amount === plan_price * 100)
        : existingStripePrices.data[0]

      if (!stripePrice?.id) return status400(res, `There is no active stripe price. Please ask admin about this.`)

      const subscriptionMetadata = {
        membership_id: membership.id,
      }

      const plan = MEMBERUP_UNIFIED_ACCOUNT_PLANS.find(
        (plan) => plan.recurringInterval === interval && plan.price * 100 === stripePrice.unit_amount,
      )
      if (!plan) {
        return res.status(500).json({ message: `There is no matching subscription plan settings.` })
      }

      const stripeCreateSubscriptionPayload: Stripe.SubscriptionCreateParams = {
        customer: stripeCustomerId,
        collection_method: 'charge_automatically',
        promotion_code: promotion_code,
        payment_behavior: 'default_incomplete',
        payment_settings: { save_default_payment_method: 'on_subscription' },
        expand: ['latest_invoice.payment_intent'],
        metadata: subscriptionMetadata,
        trial_period_days: plan.trialPeriod,
      }

      result = await stripeCreateSubscriptionMain(STRIPE_SECRET_KEY, stripeCreateSubscriptionPayload, stripePrice.id)
      await updateMembershipSetting({
        where: { id: membershipSetting.id },
        data: {
          stripe_subscription_id: result.id,
          stripe_subscription_discount_id: result.discount?.id,
        },
      })
      return res.status(200).send({ success: true, data: result })
    } catch (err: any) {
      console.log('err ====', err)
      res.status(500).end()
    }
    // try {
    //   const user = req['user']
    //   const { plan, stripe_enable_annual, collection_method, coupon } = req.body
    //   const stripeCustomerId = user.membership_setting?.stripe_customer_id
    //
    //   if (!stripeCustomerId) {
    //     return res.status(400).json({
    //       message: `Membership didn't setup stripe. Please contact the owner.`,
    //     })
    //   }
    //
    //   const updateData = { plan }
    //
    //   if (typeof stripe_enable_annual !== 'undefined') {
    //     updateData['stripe_enable_annual'] = stripe_enable_annual
    //   }
    //
    //   const stripeCreateSubscriptionPayload: Stripe.SubscriptionCreateParams = {
    //     customer: stripeCustomerId,
    //     collection_method,
    //     payment_behavior: 'default_incomplete',
    //   }
    //
    //   if (coupon) {
    //     stripeCreateSubscriptionPayload['coupon'] = coupon
    //   }
    //
    //   const interval =
    //     user.membership_setting?.stripe_enable_annual || updateData['stripe_enable_annual']
    //       ? 'year'
    //       : 'month'
    //   const productId = STRIPE_PRODUCT_IDS_MAP[plan]
    //
    //   const existingStripePrices = await stripeGetPricesMain(STRIPE_SECRET_KEY, {
    //     active: true,
    //     product: productId,
    //     recurring: { interval },
    //   })
    //   const stripePrice = existingStripePrices?.data?.[0]
    //
    //   if ((stripePrice?.id && !user.membership_setting['stripe_subscription_id']) || coupon) {
    //     const stripeSubscription = await stripeCreateSubscription(
    //       STRIPE_SECRET_KEY,
    //       stripeCreateSubscriptionPayload,
    //       stripePrice?.id
    //     )
    //
    //     updateData['stripe_subscription_id'] = stripeSubscription.id
    //     const latestInvoice = stripeSubscription.latest_invoice as Stripe.Invoice
    //     if (!latestInvoice.paid) {
    //       updateData['stripe_subscription_intent_client_secret'] = (
    //         latestInvoice.payment_intent as Stripe.PaymentIntent
    //       ).client_secret
    //       updateData['stripe_subscription_invoice_id'] = latestInvoice.id
    //     } else if (latestInvoice.paid) {
    //       user.membership_setting['stripe_subscription_intent_client_secret'] = null
    //       updateData['stripe_subscription_status'] = 'active'
    //       updateData['stripe_subscription_invoice_id'] = null
    //     }
    //   }
    //
    //   const result = await updateMembershipSetting({
    //     where: { id: user.membership_setting.id },
    //     data: updateData,
    //   })
    //   return res.status(200).send({ success: true, data: result })
    // } catch (err: any) {
    //   res.status(400).json({ message: err.message })
    // }
  })

export default handler
