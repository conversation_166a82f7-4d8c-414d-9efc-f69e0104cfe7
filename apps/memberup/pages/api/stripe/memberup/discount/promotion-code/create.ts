import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { STRIPE_SECRET_KEY } from '@memberup/shared/src/config/envs'
import authenticationMiddleware from '@/memberup/middlewares/authentication'
import { stripeCreatePromotionCodeWithDiscountMain } from '@/shared-libs/stripe'

const handler = nc<NextApiRequest, NextApiResponse>({
  onError: (err, req, res, next) => {
    // console.error(err.stack)
    res.status(500).end('Something broke!')
  },
  onNoMatch: (req, res) => {
    res.status(404).end('Api is not found')
  },
})

handler.use(authenticationMiddleware).post(async (req, res) => {
  try {
    const {
      code,
      amount_off,
      percent_off,
      name,
      duration = 'once',
      duration_in_months,
      max_redemptions,
      active = true,
    } = req.body

    if (!code) {
      return res.status(400).json({
        message: 'Promotion code is required',
      })
    }

    if (!amount_off && !percent_off) {
      return res.status(400).json({
        message: 'Either amount_off or percent_off is required',
      })
    }

    const result = await stripeCreatePromotionCodeWithDiscountMain(
      STRIPE_SECRET_KEY,
      {
        amount_off,
        percent_off,
        name,
        duration,
        duration_in_months,
      },
      {
        code,
        active,
        max_redemptions,
      },
    )

    return res.status(200).send({
      data: result,
    })
  } catch (err: any) {
    console.log('Error creating promotion code', err)
    res.status(400).json({ message: err.message })
  }
})

export default handler
