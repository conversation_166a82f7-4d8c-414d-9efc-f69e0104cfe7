import jwt from 'jsonwebtoken'
import { NextApiRequest, NextApiResponse } from 'next'
import nc from 'next-connect'

import { errorHand<PERSON> } from '@memberup/shared/src/libs/prisma/error-handler'

const secretKey = process.env.NEXTAUTH_SECRET

const handler = nc<NextApiRequest, NextApiResponse>()

handler.post(async (req, res) => {
  try {
    const { email, password, redirectTo } = req.body

    const token = jwt.sign({ email, password, redirectTo }, secretKey)

    console.log(token)

    return res.status(200).json({ success: true, token })
  } catch (err: any) {
    console.log('membership err ==== ', err)
    res.status(400).json(errorHandler(err, 'Membership'))
  }
})

export default handler
