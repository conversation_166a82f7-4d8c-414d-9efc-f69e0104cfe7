import { resolve } from 'path'
import { TextEncoder } from 'util'
import { config } from 'dotenv'

import { clearFixtures, loadFixtures } from './__test__/fixtures'
import prisma from '@memberup/shared/src/libs/prisma/prisma'

// Load test environment variables from .env.test (overrides Doppler vars)
config({
  path: resolve(process.cwd(), '../../.env.test'),
  override: true,
})

declare global {
  var TextEncoder: typeof import('util').TextEncoder
}
global.TextEncoder = TextEncoder

beforeEach(async () => {
  const { testPath } = expect.getState()
  if (testPath && testPath.includes('__test__/api')) {
    await loadFixtures()
  }
})

afterEach(async () => {
  const { testPath } = expect.getState()
  if (testPath && testPath.includes('__test__/api')) {
    await clearFixtures()
  }
})

afterAll(async () => {
  const { testPath } = expect.getState()
  if (testPath && testPath.includes('__test__/api')) {
    await prisma.$disconnect()
  }
})
