import { use<PERSON>ara<PERSON> } from 'next/navigation'
import { useEffect, useRef, useState } from 'react'
import { type MessageFilters, type SearchOptions } from 'stream-chat'
import { useChatContext } from 'stream-chat-react'

import { getUserActivityCalendar } from '@memberup/shared/src/services/apis/user.api'
import { FEED_STATUS_ENUM } from '@memberup/shared/src/types/enum'
import { TActivityCalendarData, TActivityCalendarDay } from '@/components/visualization/activity-calendar/types'
import { useStore } from '@/hooks/useStore'
import { getCurrentDateDayDateObject, getDayDateObject } from '@/shared-libs/date-utils'
import { IMembership, IUser } from '@/shared-types/interfaces'

const pageSize = 10
const maxCommentsPerPost = 2
const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

const getLevel = (activities: number) => {
  switch (activities) {
    case 0:
      return 0
    case 1:
    case 2:
      return 1
    case 3:
    case 4:
    case 5:
      return 2
    case 6:
    case 7:
    case 8:
    case 9:
      return 3
    default:
      return 4
  }
}

const useUserProfileData = (userData: IUser, selectedCommunity: IMembership | null) => {
  const { client: streamClient } = useChatContext()
  const { connecting: streamChatConnecting, connected: streamChatConnected } = useStore((state) => state.streamChat)
  const user = useStore((state) => state.auth.user)

  const activityCalendarInitializedRef = useRef(false)
  const streamChannelListenersRef = useRef([])
  const streamChannelListenersInitiated = useRef(false)
  const { slug } = useParams()
  const slugRef = useRef(slug)
  const selectedCommunityRef = useRef(selectedCommunity)
  const username = slug.slice(3) as string
  const messagesInitialized = useRef(false)

  const userPosts = useRef(new Set())
  const fetchedPosts = useRef(new Set())
  const displayedPosts = useRef(new Set())
  const displayedComments = useRef(new Set())
  const [activityCalendarData, setActivityCalendarData] = useState<TActivityCalendarData>(null)
  const [posts, setPosts] = useState([])
  const [postComments, setPostComments] = useState({})
  const postsQueue = useRef([])
  const newComments = useRef({})
  const userPostsNext = useRef(null)
  const userCommentsNext = useRef(null)
  const [loadingPosts, setLoadingPosts] = useState(false)
  const [loadingActivityCalendar, setLoadingActivityCalendar] = useState(true)
  const [hasMore, setHasMore] = useState(true)

  const resetState = () => {
    fetchedPosts.current.clear()
    displayedPosts.current.clear()
    postsQueue.current = []
    newComments.current = {}
    userPostsNext.current = null
    userCommentsNext.current = null
    setPosts([])
    setPostComments({})
    //setLoadingPosts(true)
    setHasMore(true)
  }

  const setupStreamListeners = async () => {
    if (!streamChatConnected || streamChatConnecting) return
    const selectedChannelsIds = selectedCommunity.channels.map((space: any) => space.id)

    const filter = { id: { $in: selectedChannelsIds } }
    const sort: any = { id: -1 }
    const options = {
      watch: true,
      limit: 30,
    }

    const channels = await streamClient.queryChannels(filter, sort, options)

    for (let channel of channels) {
      streamChannelListenersRef.current.push(
        channel.on('message.new', ({ message }: any) => {
          if (message.user.id === userData.id) {
            if (message.parent_id) {
              const updatedPostComments = { ...postComments }

              if (updatedPostComments[message.parent_id] === undefined) {
                updatedPostComments[message.parent_id] = []
              }

              updatedPostComments[message.parent_id].push(message)
              displayedComments.current.add(message.id)
              setPostComments(updatedPostComments)
            } else {
              if (!displayedPosts.current.has(message.id)) {
                setPosts((prevPosts) => [message, ...prevPosts])
                displayedPosts.current.add(message.id)
              }
            }
          }
        }),
      )

      streamChannelListenersRef.current.push(
        channel.on('message.updated', ({ message }: any) => {
          const updatedPostComments = { ...postComments }

          if (message.parent_id && updatedPostComments[message.parent_id]) {
            const index = updatedPostComments[message.parent_id].findIndex((comment: any) => comment.id === message.id)

            if (index !== -1) {
              updatedPostComments[message.parent_id][index] = message
            }

            updatedPostComments[message.parent_id].forEach((comment: any) => {
              if (comment.replies && comment.replies.length) {
                const replyIndex = comment.replies.findIndex((reply: any) => reply.id === message.id)

                if (replyIndex !== -1) {
                  comment.replies[replyIndex] = message
                }
              }
            })
          } else {
            setPosts(posts.map((post) => (post.id === message.id ? message : post)))
          }

          setPostComments(updatedPostComments)
        }),
      )

      streamChannelListenersRef.current.push(
        channel.on('message.deleted', ({ message }: any) => {
          if (message.parent_id) {
            const updatedPostComments = { ...postComments }

            updatedPostComments[message.parent_id] = updatedPostComments[message.parent_id].filter(
              (comment: any) => comment.id !== message.id,
            )

            updatedPostComments[message.parent_id].forEach((comment: any) => {
              if (comment.replies && comment.replies.length) {
                comment.replies = comment.replies.filter((reply: any) => reply.id !== message.id)
              }
            })

            if (displayedComments.current.has(message.id)) displayedComments.current.delete(message.id)

            setPostComments(updatedPostComments)
          } else {
            setPosts(posts.filter((post) => post.id !== message.id))
          }
        }),
      )
    }

    streamChannelListenersInitiated.current = true
  }

  useEffect(() => {
    if (
      !selectedCommunity ||
      !streamChannelListenersRef.current ||
      streamChannelListenersInitiated.current ||
      !userData ||
      loadingPosts ||
      !streamChatConnected ||
      streamChatConnecting
    )
      return
    setupStreamListeners()

    return () => {
      streamChannelListenersRef.current.forEach((listener) => listener?.unsubscribe?.())
      //streamChannelListenersRef.current = null
    }
  }, [selectedCommunity, loadingPosts, streamClient, userData, streamChatConnected, streamChatConnecting])

  const fetchStreamMessages = async (filters: MessageFilters, options: SearchOptions = {}) => {
    if (!streamChatConnected || streamChatConnecting) return { next: 'EOF', results: [] }
    const spaceIds = selectedCommunity.channels.map((space: any) => space.id) || []
    const channelFilters = { id: { $in: spaceIds } }
    const messageFilters = {
      ...filters,
      deleted_at: { $exists: false },
      feed_status: { $nin: [FEED_STATUS_ENUM.rejected] },
    }

    const searchResponse = await streamClient.search(channelFilters, messageFilters, {
      sort: { created_at: -1 },
      ...options,
    })

    return {
      next: searchResponse.next || 'EOF',
      results: searchResponse.results.map((message) => message.message),
    }
  }

  const fetchUserPosts = async () => {
    const options: SearchOptions = { limit: pageSize }

    if (userPostsNext.current) options.next = userPostsNext.current

    const { next, results } = await fetchStreamMessages(
      { 'user.id': userData.id, parent_id: { $exists: false } },
      options,
    )

    results.forEach((post) => userPosts.current.add(post.id))

    userPostsNext.current = next
    return results
  }

  const enqueuePosts = (posts: any) => {
    posts.forEach((post: any) => {
      if (!fetchedPosts.current.has(post.id)) {
        fetchedPosts.current.add(post.id)
        if (post.user.id === userData.id) userPosts.current.add(post.id)
        postsQueue.current.push(post)
      }
    })
  }

  const fetchUserComments = async () => {
    const options: SearchOptions = { limit: 50 }

    if (userCommentsNext.current) options.next = userCommentsNext.current

    const { next, results } = await fetchStreamMessages(
      {
        'user.id': userData.id,
        parent_id: {
          $exists: true,
        },
      },
      options,
    )

    userCommentsNext.current = next
    return results
  }

  const fetchUserCommentedPosts = async () => {
    const userComments = await fetchUserComments()

    if (!userComments.length) return

    const postsToFetch = new Set<string>()

    const commentsToFetch = new Set<string>()

    const isReply = (message: any) => message.reply_parent_id && message.reply_parent_id !== message.parent_id

    userComments.forEach((comment) => {
      if (!fetchedPosts.current.has(comment.parent_id)) {
        postsToFetch.add(comment.parent_id)
      }
    })

    if (postsToFetch.size) {
      const { results: posts } = await fetchStreamMessages({
        id: { $in: Array.from(postsToFetch) },
      })

      enqueuePosts(posts)
    }

    userComments.forEach((comment) => {
      if (
        displayedComments.current.has(comment.id) ||
        userPosts.current.has(comment.parent_id) ||
        commentsToFetch.has(comment.id) ||
        (postComments[comment.parent_id] && postComments[comment.parent_id].length >= maxCommentsPerPost)
      )
        return

      if (newComments.current[comment.parent_id] === undefined) newComments.current[comment.parent_id] = []

      if (isReply(comment)) {
        commentsToFetch.add(comment.reply_parent_id as string)
      } else if (newComments.current[comment.parent_id].length < maxCommentsPerPost) {
        newComments.current[comment.parent_id].push(comment)
      }
    })

    if (commentsToFetch.size) {
      const { results: comments } = await fetchStreamMessages({
        id: { $in: Array.from(commentsToFetch) },
      })

      if (comments) {
        comments.forEach((comment) => newComments.current[comment.parent_id].push(comment))
      }
    }

    Object.keys(newComments.current).forEach((postId) => {
      newComments.current[postId].sort(
        (a: any, b: any) => new Date(a.created_at).valueOf() - new Date(b.created_at).valueOf(),
      )
    })
  }

  const fetchMessages = async () => {
    if (
      postsQueue.current.length < pageSize &&
      (userPostsNext.current !== 'EOF' || userCommentsNext.current !== 'EOF')
    ) {
      if (!loadingPosts) setLoadingPosts(true)

      if (userPostsNext.current !== 'EOF') {
        const userPosts = await fetchUserPosts()

        if (userPosts.length) {
          enqueuePosts(userPosts)
        }
      }

      if (userCommentsNext.current !== 'EOF') {
        await fetchUserCommentedPosts()
      }

      postsQueue.current.sort((a, b) => new Date(b.created_at).valueOf() - new Date(a.created_at).valueOf())
    }

    if (postsQueue.current.length) {
      const newPosts = postsQueue.current.splice(0, pageSize)

      newPosts.forEach((post) => {
        displayedPosts.current.add(post.id)
      })

      setPosts((prevPosts) => [...prevPosts, ...newPosts])

      if (newComments.current) {
        const updatedPostComments = { ...postComments }

        Object.keys(newComments.current).forEach((postId) => {
          if (updatedPostComments[postId] === undefined) {
            updatedPostComments[postId] = []
          }

          updatedPostComments[postId].push(
            ...newComments.current[postId].filter((newComment: any) => {
              if (!displayedComments.current.has(newComment.id)) {
                displayedComments.current.add(newComment.id)
                return true
              }

              return false
            }),
          )
        })

        setPostComments(updatedPostComments)
        newComments.current = {}
      }
    } else {
      setHasMore(false)
    }

    setLoadingPosts(false)
  }

  const fetchActivityCalendar: () => Promise<TActivityCalendarData | void> = async () => {
    setLoadingActivityCalendar(true)
    let month = null
    const data: TActivityCalendarDay[][] = new Array(7).fill(null).map(() => [])

    try {
      let {
        data: { data: calendarData },
      } = await getUserActivityCalendar(username)
      const calendarByDay = {}

      calendarData.forEach((dayData: TActivityCalendarDay) => {
        calendarByDay[dayData.day] = dayData
      })

      let currentDateObj = getDayDateObject(new Date(calendarData[0].day))
      let week = new Array(7).fill(null)
      let lastDay = getDayDateObject(new Date(calendarData[calendarData.length - 1].day))

      const today = getCurrentDateDayDateObject()

      if (today > lastDay) lastDay = today

      const yearAgoDate = getDayDateObject(new Date(new Date().setUTCFullYear(new Date().getUTCFullYear() - 1)))

      if (yearAgoDate < currentDateObj) {
        currentDateObj = yearAgoDate
      }

      const currentDateObjDay = currentDateObj.getUTCDay()

      // Ensure it starts on a Monday
      if (currentDateObj.getUTCDay() !== 1)
        currentDateObj.setUTCDate(currentDateObj.getUTCDate() - (currentDateObjDay === 0 ? 6 : currentDateObjDay - 1))

      let weekNumber = 0

      do {
        let weekDay = currentDateObj.getUTCDay()
        const chartOrder = weekDay === 0 ? 6 : weekDay - 1
        const dayData = calendarByDay[currentDateObj.toISOString()]
        const newDay = new Date(currentDateObj.toISOString())

        if (dayData !== undefined) {
          const { count } = dayData

          week[chartOrder] = {
            day: newDay,
            level: getLevel(count),
            count,
          }
        } else {
          week[chartOrder] = {
            day: newDay,
            level: 0,
            count: 0,
          }
        }

        if (weekDay === 0 || currentDateObj.toISOString() === lastDay.toISOString()) {
          ;[0, 1, 2, 3, 4, 5, 6].forEach((day: any, index: number) => {
            data[index].push(week[day])
          })

          weekNumber++
          month = null

          if (currentDateObj.getUTCDate() <= 7) {
            month = monthNames[currentDateObj.getUTCMonth() - 1]
          }

          week = new Array(7).fill(null)
        }

        currentDateObj.setUTCDate(currentDateObj.getUTCDate() + 1)
      } while (currentDateObj <= lastDay)

      const difference = data[0].length - 51

      if (difference > 0) {
        for (let i = 0; i < data.length; i++) {
          data[i].splice(0, difference)
        }
      }

      const monthsRow = []

      const currentItem = {
        month: '',
        colspan: 1,
      }

      for (let i = data[0].length - 1; i >= 0; i--) {
        if (!data[6][i]) {
          currentItem.colspan++
          continue
        }

        const dayDate = new Date(data[6][i].day)

        currentItem.month = monthNames[dayDate.getUTCMonth()]

        if (dayDate.getUTCDate() <= 7) {
          const newItem = {
            colspan: currentItem.colspan,
            month: currentItem.month,
          }

          monthsRow.unshift(newItem)
          currentItem.colspan = 1
        } else {
          currentItem.colspan++
        }
      }

      monthsRow.unshift({
        month: '',
        colspan: currentItem.colspan - 1,
      })

      setActivityCalendarData({
        monthsRow,
        data,
      })
      setLoadingActivityCalendar(false)
    } catch (e) {
      console.error(e)
      setLoadingActivityCalendar(false)
    }
  }

  useEffect(() => {
    // if (messagesInitialized.current || !userData || !selectedCommunity) {
    //   return
    // }

    if (!userData || !selectedCommunity || !streamChatConnected || streamChatConnecting) {
      return
    }

    fetchMessages()
  }, [userData, streamClient?.user, selectedCommunity, streamChatConnected, streamChatConnecting])

  useEffect(() => {
    if (!streamClient?.user) {
      return
    }
    if (slugRef.current !== slug || !activityCalendarInitializedRef.current) {
      resetState()
      fetchActivityCalendar()
      slugRef.current = slug
      activityCalendarInitializedRef.current = true
    }
  }, [slug, streamClient?.user])

  useEffect(() => {
    if (!streamClient?.user || !selectedCommunity || !streamChatConnected || streamChatConnecting) {
      return
    }
    if (selectedCommunityRef.current !== selectedCommunity) {
      resetState()
      selectedCommunityRef.current = selectedCommunity
      fetchMessages()
    }
  }, [selectedCommunity, streamClient?.user, streamChatConnected, streamChatConnecting])

  return {
    activityCalendarData,
    fetchMessages,
    hasMore,
    loadingActivityCalendar,
    loadingPosts,
    posts,
    postComments,
  }
}

export { useUserProfileData }
