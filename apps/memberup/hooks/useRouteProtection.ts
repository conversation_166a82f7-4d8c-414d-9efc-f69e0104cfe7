'use client'

import { usePathname, useRouter } from 'next/navigation'
import { useEffect, useRef } from 'react'

import { useStore } from '@/hooks/useStore'
import { getRedirectPath, isProtectedRoute } from '@/lib/route-protection.utils'

export function useRouteProtection() {
  const user = useStore((state) => state.auth.user)
  const router = useRouter()
  const pathname = usePathname()
  const hasRedirected = useRef(false)

  useEffect(() => {
    // Don't run if we already redirected
    if (hasRedirected.current) return

    if (!isProtectedRoute(pathname)) {
      return
    }

    if (!user) {
      hasRedirected.current = true
      const redirectPath = getRedirectPath(pathname)
      if (redirectPath) {
        router.push(redirectPath)
      }
    }
  }, [user, pathname, router])

  // Reset redirect flag when user changes
  useEffect(() => {
    if (user) {
      hasRedirected.current = false
    }
  }, [user?.id])
}
