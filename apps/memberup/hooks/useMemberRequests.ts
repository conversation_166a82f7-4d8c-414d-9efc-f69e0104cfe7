import { useCallback } from 'react'
import useSWR from 'swr'

import { useStore } from './useStore'
import { toast } from '@/components/ui/sonner'
import { unexpectedError } from '@/lib/error-messages'
import { getMemberRequestsApi } from '@/shared-services/apis/membership.api'
import { USER_ROLE_ENUM } from '@/shared-types/enum'

const POLLING_INTERVAL = 60000 // 1 minute

export function useMemberRequests() {
  const membership = useStore((state) => state.community.membership)
  const userMembership = useStore((state) => state.community.userMembership)
  const memberRequestsFromStore = useStore((state) => state.community.memberRequests)
  const setMemberRequests = useStore((state) => state.community.setMemberRequests)
  const setLoadingMemberRequests = useStore((state) => state.community.setLoadingMemberRequests)

  const shouldCheckMemberRequests = Boolean(userMembership?.user_role !== USER_ROLE_ENUM.member && membership?.id)

  const { data, mutate, isLoading } = useSWR(
    shouldCheckMemberRequests ? ['memberRequests', membership?.id] : null,
    async () => {
      setLoadingMemberRequests(true)
      try {
        const res = await getMemberRequestsApi(membership!.id)
        const requests = res.data.data
        setMemberRequests(requests) // Update Zustand store for computed properties
        return requests
      } catch (error) {
        // Only show error toast if it's not a 401/403 error
        if (error?.response?.status !== 401 && error?.response?.status !== 403) {
          toast.error(unexpectedError)
        }
        throw error // SWR handles the error state
      } finally {
        setLoadingMemberRequests(false)
      }
    },
    {
      refreshInterval: shouldCheckMemberRequests ? POLLING_INTERVAL : 0, // Poll every minute when active
      revalidateOnFocus: true, // Revalidate when window regains focus
      dedupingInterval: 2000, // Dedupe requests within 2 seconds
      revalidateOnMount: true, // Always fetch on mount
      keepPreviousData: true, // Keep previous data while fetching new data
    },
  )

  // Manual fetch function (useful for mutations that need to refresh the data)
  const fetchMemberRequests = useCallback(async () => {
    await mutate()
  }, [mutate])

  // Use data from SWR or fall back to Zustand store
  const memberRequests = data || memberRequestsFromStore || []
  const hasMemberRequests = memberRequests.length > 0

  return {
    memberRequests,
    loading: isLoading,
    fetchMemberRequests,
    hasMemberRequests,
  }
}
