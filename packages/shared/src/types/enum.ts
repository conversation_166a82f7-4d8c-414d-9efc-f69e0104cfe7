export {
  ACTION_NAME_ENUM,
  AFFILIATE_ATTRIBUTION_STATUS_ENUM,
  AFFILIATE_PAYOUT_ATTEMPT_STATUS_ENUM,
  BILLING_CYCLE_ENUM,
  CHANNEL_TYPE_ENUM,
  COMMUNITY_PAYMENT_STATUS_ENUM,
  EVENT_LOCATION_TYPE_ENUM,
  EVENT_STATUS_ENUM,
  FEED_STATUS_ENUM,
  FEED_TYPE_ENUM,
  LIBRARY_CONTENT_ENUM,
  LIBRARY_LAYOUT_ENUM,
  LIVE_STREAM_BRODCAST_ENUM,
  MEMBERSHIP_PAYMENT_ATTEMPT_STATUS_ENUM,
  MEMBERSHIP_PAYMENT_STATUS_ENUM,
  MEMBERUP_PLAN_ENUM,
  MESSAGE_STATUS_ENUM,
  PERSONALITY_TYPE_ENUM,
  SPACE_TYPE_ENUM,
  STRIPE_SUBSCRIPTION_STATUS_ENUM,
  THEME_MODE_ENUM,
  USER_ROLE_ENUM,
  USER_STATUS_ENUM,
  VISIBILITY_ENUM,
  USER_MEMBERSHIP_STATUS_ENUM,
  PAYMENT_TYPE_ENUM,
  PAYMENT_STATUS_ENUM,
} from '@prisma/client'

export enum ACTIVITY_ENUM {
  library = 'library',
  live = 'live',
  post = 'post',
}

export enum BACKGROUND_TYPE_ENUM {
  html = 'html',
  image = 'image',
}

export enum BANNER_IMAGE_SIZE_ENUM {
  short = 'short',
  tall = 'tall',
  long = 'long',
}

export enum BIO_QUESTION_ENUM {
  multiple_choice = 'multiple_choice',
  short_text = 'short_text',
  phone_number = 'phone_number',
  address_list = 'address_list',
}

export enum COVER_IMAGE_TYPE_ENUM {
  default = 'default',
  image = 'image',
}

export enum DISCOUNT_TYPE_ENUM {
  free_trial = 'free_trial',
  fixed_amount = 'fixed_amount',
}

export enum EVENT_VIEW_TYPE_ENUM {
  draft = 'Draft',
  upcoming = 'Upcoming',
  past = 'Past',
}

export enum GETSTREAM_FEED_GROUP_ENUM {
  flat = 'flat',
  aggregated = 'aggregated',
  notification = 'notification',
}

export enum KNOCK_OBJECT_ENUM {
  new_content = 'new-content',
  new_event = 'new-event',
  new_spark = 'new-spark',
  new_everyone_mention = 'new-everyone-mention',
}

export enum KNOCK_WORKFLOW_ENUM {
  email_verification = 'email-verification',
  creator_account_confirmation = 'creator-account-confirmation',
  content = 'content',
  event = 'event',
  invite_member = 'invite-member',
  new_like = 'new-like-notification',
  new_message = 'new-message-notification',
  new_member_welcome = 'new-member-welcome-email',
  community_name_changed = 'community-name-changed',
  new_member = 'new-member-notification',
  reset_email = 'reset-email',
  reset_password = 'reset-password',
  verify_email = 'verify-email',
  spark = 'spark-reminder',
  new_comment_notification = 'new-comment-notification',
  new_mention_notification = 'new-mention-notification',
  new_everyone_mention_notification = 'new-everyone-mention-notification',
  cancel_membership = 'cancel-membership',
  cancel_event = 'cancel-event',
  feed_post_moderation_report_notification = 'feed-post-moderation-report-notification',
}

export enum MESSAGE_TYPE_ENUM {
  regular = 'regular', // A regular message created in the channel.
  ephemeral = 'ephemeral', // A temporary message which is only delivered to one user. It is not stored in the channel history. Ephemeral messages are normally used by commands (e.g. /giphy) to prompt messages or request for actions.
  error = 'error', // An error message generated as a result of a failed command. It is also ephemeral, as it is not stored in the channel history and is only delivered to one user.
  reply = 'reply', // A message in a reply thread. Messages created with parent_id are automatically of this type.
  system = 'system', // A message generated by a system event, like updating the channel or muting a user.
  deleted = 'deleted', // A soft deleted message
}

export enum MUX_STATUS_ENUM {
  cancelled_upload = 'cancelled_upload',
  created = 'created',
  ready = 'ready',
  waiting_for_upload = 'waiting_for_upload',
}

export enum LAYOUT_ENUM {
  auth_layout2 = 'auth_layout',
  auth_layout = 'auth_layout2',
  live_layout = 'live_layout',
  normal_layout = 'normal_layout',
  page_layout = 'page_layout',
  main_layout = 'main_layout',

  page_layout_legacy = 'page_layout_legacy',
}

export enum LIVE_SIGNAL_STATUS_ENUM {
  good = 'good',
  low = 'low',
  very_low = 'very-low',
}

export enum RECURRING_INTERVAL_ENUM {
  month = 'month',
  year = 'year',
}

export enum PROMO_CODE_ERROR_CODE_ENUM {
  MAX_REDEMPTIONS_REACHED = 'MAX_REDEMPTIONS_REACHED',
  COUPON_INVALID = 'COUPON_INVALID',
  NOT_FOUND = 'NOT_FOUND',
}

export enum STRIPE_BALANCE_TYPE_ENUM {
  adjustment = 'adjustment',
  advance = 'advance',
  advance_funding = 'advance_funding',
  anticipation_repayment = 'anticipation_repayment',
  application_fee = 'application_fee',
  application_fee_refund = 'application_fee_refund',
  charge = 'charge',
  connect_collection_transfer = 'connect_collection_transfer',
  contribution = 'contribution',
  issuing_authorization_hold = 'issuing_authorization_hold',
  issuing_authorization_release = 'issuing_authorization_release',
  issuing_dispute = 'issuing_dispute',
  issuing_transaction = 'issuing_transaction',
  payment = 'payment',
  payment_failure_refund = 'payment_failure_refund',
  payment_refund = 'payment_refund',
  payout = 'payout',
  payout_cancel = 'payout_cancel',
  payout_failure = 'payout_failure',
  refund = 'refund',
  refund_failure = 'refund_failure',
  reserve_transaction = 'reserve_transaction',
  reserved_funds = 'reserved_funds',
  stripe_fee = 'stripe_fee',
  stripe_fx_fee = 'stripe_fx_fee',
  tax_fee = 'tax_fee',
  topup = 'topup',
  topup_reversal = 'topup_reversal',
  transfer = 'transfer',
  transfer_cancel = 'transfer_cancel',
  transfer_failure = 'transfer_failure',
  transfer_refund = 'transfer_refund',
}

export enum USER_PLAN_ENUM {
  month = 'month',
  annual = 'annual',
  lifetime = 'lifetime',
}

export enum ResetPasswordErrorCode {
  INVALID_USER = 'INVALID_USER',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  INVALID_TOKEN = 'INVALID_TOKEN',
}
