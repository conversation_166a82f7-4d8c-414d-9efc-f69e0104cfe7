/**
 * Interface for coupon/discount details
 */
export interface DiscountDetails {
  amount_off?: number // Fixed amount discount (in cents for Stripe, dollars for display)
  percent_off?: number // Percentage discount (0-100)
}

/**
 * Calculate discount amount based on coupon details and original price
 *
 * @param originalPrice - The original price before discount (in dollars)
 * @param discountDetails - Coupon/discount details containing amount_off or percent_off
 * @param convertFromCents - Whether to convert amount_off from cents to dollars (default: true)
 * @returns The discount amount in dollars (unrounded)
 *
 * @example
 * // Fixed amount discount (in dollars)
 * calculateDiscountAmount(100, { amount_off: 15 }) // Returns 15
 *
 * // Percentage discount
 * calculateDiscountAmount(100, { percent_off: 20 }) // Returns 20
 *
 * // No discount
 * calculateDiscountAmount(100, {}) // Returns 0
 */
export const calculateDiscountAmount = (
  originalPrice: number,
  discountDetails: DiscountDetails,
  convertFromCents: boolean = true,
): number => {
  if (originalPrice == null || originalPrice < 0) {
    return 0
  }

  if (!discountDetails) {
    return 0
  }

  let discountAmount = 0

  // Fixed amount discount
  if (discountDetails.amount_off && discountDetails.amount_off > 0) {
    discountAmount = convertFromCents ? discountDetails.amount_off / 100 : discountDetails.amount_off
  }
  // Percentage discount
  else if (discountDetails.percent_off && discountDetails.percent_off > 0) {
    discountAmount = (discountDetails.percent_off / 100) * originalPrice
  }

  // Ensure discount doesn't exceed original price
  discountAmount = Math.min(discountAmount, originalPrice)
  return Math.max(0, discountAmount)
}
