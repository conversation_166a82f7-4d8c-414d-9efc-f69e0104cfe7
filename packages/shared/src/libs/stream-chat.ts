import { captureException as sentryCaptureException } from '@sentry/nextjs'
import { DeleteUserOptions, StreamChat } from 'stream-chat'

import { getFullName } from './profile'
import { GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET } from '@/shared-config/envs'
import { CHANNEL_TYPE_ENUM, USER_ROLE_ENUM } from '@/shared-types/enum'
import { IUser } from '@/shared-types/interfaces'
import { TAppCropArea } from '@/shared-types/types.ts'

let streamChatServerClient: StreamChat

export const getStreamChatServerClient = () => {
  try {
    if (streamChatServerClient) return streamChatServerClient
    streamChatServerClient = new StreamChat(GET_STREAM_APP_KEY, GET_STREAM_APP_SECRET, {
      timeout: 30000,
    })
    // await streamChatServerClient.updateAppSettings({
    //   multi_tenant_enabled: true,
    //   permission_version: 'v2',
    //   migrate_permissions_to_v2: true,
    // })
    return streamChatServerClient
  } catch (err) {
    console.log('err ======', err)
    return null
  }
}

export const mapUserToStreamUser = (user: Partial<IUser>) => {
  const isAdminOrOwner = [USER_ROLE_ENUM.admin, USER_ROLE_ENUM.owner].includes(
    user.role || (USER_ROLE_ENUM.member as any),
  )
  return {
    id: user.id,
    name: getFullName(user.first_name, user.last_name),
    username: user.username,
    image: user.profile?.image || user.image,
    image_crop_area: user.profile?.image_crop_area || user.image_crop_area,
    role: isAdminOrOwner ? 'global_admin' : 'global_moderator',
    status: user.status,
  }
}

export const createStreamChatUserToken = async (id: string) => {
  const client = getStreamChatServerClient()
  const result = client.createToken(id)
  return result
}

export const streamChatUpsertUser = async (
  user: Partial<IUser> & {
    id: string
    first_name: string
    last_name: string
    image: string
    role: string
    status: string
    image_crop_area: TAppCropArea
  },
  teams: string[],
) => {
  const client = getStreamChatServerClient()

  try {
    await client.reactivateUser(user.id)
  } catch (err) {
    console.warn('reactive err ====', err)
  }

  const payload = mapUserToStreamUser(user)
  const result = await client.upsertUser({ ...payload, teams })
  return result?.users?.[user.id]
}

export const streamChatDeleteUser = async (id: string, options: DeleteUserOptions) => {
  const client = getStreamChatServerClient()
  const result = await client.deleteUsers([id], options)
  return result
}

export const streamChatDeactiveUser = async (
  id: string,
  params?: {
    created_by_id?: string
    mark_messages_deleted?: boolean
  },
) => {
  const client = getStreamChatServerClient()
  const result = await client.deactivateUser(id, params)
  return result.user
}

export const streamChatReactivateUser = async (
  id: string,
  params?: {
    created_by_id?: string
    name?: string
    restore_messages?: boolean
  },
) => {
  const client = getStreamChatServerClient()
  const result = await client.reactivateUser(id, params)
  return result.user
}

export const getStreamChannels = async (filter: any, sort?: any) => {
  // const newFilter = { type: 'teams', members: { $in: ['thierry'] } }
  // const sort = [{ last_message_at: -1 }]
  const client = getStreamChatServerClient()
  const channels = await client.queryChannels(filter)
  //   , sort, {
  //     watch: true, // this is the default
  //     state: true,
  // }
  return channels
}

export const createStreamChannel = async (
  channelType: CHANNEL_TYPE_ENUM,
  channelId: string,
  channelData: {
    created_by_id: string
    name: string
    team: string
    members?: string[]
    custom_type?: string
    custom_started?: boolean
  }, // custom_type: library | live | spark | space
) => {
  const client = getStreamChatServerClient()
  const channel = client.channel(channelType, channelId, channelData)
  return await channel.create()
}

export const deleteStreamChannel = async (channelId: string) => {
  const client = getStreamChatServerClient()
  const result = await client.deleteChannels([channelId], { hard_delete: true })
  return result
}

export const deleteStreamChannels = async (channelIds: string[]) => {
  const client = getStreamChatServerClient()
  const result = await client.deleteChannels(channelIds, { hard_delete: true })
  return result
}

export const updateStreamChannel = async (
  channelType: CHANNEL_TYPE_ENUM,
  channelId: string,
  set: any,
  unset: string[],
) => {
  const client = getStreamChatServerClient()
  const channel = client.channel(channelType, channelId)
  await channel.updatePartial({
    set,
    unset,
  })
  return channel
}

export const createStreamMessage = async (
  channelType: CHANNEL_TYPE_ENUM,
  channelId: string,
  messageData: {
    id: string
    space_type?: string
    feed_type?: string
    parent_id?: string
    html?: string
    text?: string
    attachments?: any[]
    user_id?: string
    mentioned_users?: any[]
    message_custom?: any
    metadata?: any
    permalink?: string
    m_question_id?: string
  },
  options?: { skip_enrich_url?: boolean; skip_push?: boolean },
) => {
  try {
    const client = getStreamChatServerClient()
    const channel = client.channel(channelType, channelId)
    await channel.create()
    const result = await channel.sendMessage(messageData, options)
    return result
  } catch (err: any) {
    sentryCaptureException(err)
    console.error('createMessage Error =====', err.message)
  }
}

export const deleteStreamMessage = async (messageId: string) => {
  try {
    const client = getStreamChatServerClient()
    // IMPORTANT: Hard delete
    const hardDelete = true
    const result = await client.deleteMessage(messageId, hardDelete)
    return result
  } catch (err: any) {
    console.error('deleteStreamMessage Error =====', err.message)
  }
}

export const getStreamMessage = async (messageId: string) => {
  try {
    const client = getStreamChatServerClient()
    const result = await client.getMessage(messageId)
    return result
  } catch (err: any) {
    // NOTE: We ignore exceptions for not finding a message.
    return null
  }
}

export const updateStreamMessage = async (messageId: string, userId: string, set?: any, unset?: string[]) => {
  try {
    const client = getStreamChatServerClient()
    const result = await client.partialUpdateMessage(messageId, { set, unset }, userId)
    return result
  } catch (err: any) {
    console.error('updateMessage Error =====', err.message)
  }
}

export const createStreamLike = async (
  channelType: CHANNEL_TYPE_ENUM,
  channelId: string,
  messageId: string,
  reactionData: {
    id: string
    user_id: string
    type: string
  },
) => {
  try {
    const client = getStreamChatServerClient()
    const channel = client.channel(channelType, channelId)
    const result = await channel.sendReaction(messageId, reactionData)
    return result
  } catch (err: any) {
    console.error('createMessage Error =====', err.message)
  }
}

export const deleteStreamLike = async (
  channelType: CHANNEL_TYPE_ENUM,
  channelId: string,
  messageId: string,
  userId: string,
) => {
  try {
    console.log('deleting like on getstream')
    const client = getStreamChatServerClient()
    const channel = client.channel(channelType, channelId)
    const result = await channel.deleteReaction(messageId, 'like')
    return result
  } catch (err: any) {
    if (err.code === 16) {
      // Handle the StreamChat error NOT FOUND
      console.error('deleteStreamReaction Error =====', err.message)
      return
    }
    console.error('deleteStreamReaction Error =====', err.message)
  }
}

export const createStreamReaction = async (
  channelType: CHANNEL_TYPE_ENUM,
  channelId: string,
  messageId: string,
  reactionData: {
    id: string
    user_id: string
    type: string
    text?: string
    enforce_unique?: boolean
    mentioned_users?: string[]
  },
) => {
  try {
    const client = getStreamChatServerClient()
    const channel = client.channel(channelType, channelId)
    const result = await channel.sendReaction(messageId, reactionData)
    return result
  } catch (err: any) {
    console.error('createMessage Error =====', err.message)
  }
}

export const deleteStreamReaction = async (messageId: string, type: string, userId: string) => {
  try {
    const message = await getStreamMessage(messageId)
    if (message) {
      const client = getStreamChatServerClient()
      const channel = client.channel(message.message?.channel?.type as string, message.message?.channel?.id)

      const result = await channel.deleteReaction(messageId, type, userId)
      return result
    }
    return null
  } catch (err: any) {
    if (err.code === 16) {
      // Handle the StreamChat error NOT FOUND
      return
    }
    console.error('deleteStreamReaction Error =====', err.message)
  }
}
