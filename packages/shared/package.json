{"name": "@memberup/shared", "version": "0.0.0", "type": "module", "description": "memberup shared", "author": "", "homepage": "", "license": "ISC", "main": "src/index.ts", "types": "src/index.ts", "scripts": {"lint": "eslint .", "lint:fix": "eslint . --fix"}, "dependencies": {"@cloudinary/react": "^1.11.2", "@cloudinary/url-gen": "^1.13.0", "@knocklabs/node": "0.4.25", "@mui/icons-material": "^5.0.0 <6.0.0", "@mui/material": "^5.0.0 <6.0.0", "@mux/mux-player-react": "^3.3.0", "@react-google-maps/api": "^2.19.2", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/full-screen": "^3.12.0", "@react-pdf-viewer/page-navigation": "^3.12.0", "algoliasearch": "^4.20.0", "bcryptjs": "^2.4.3", "cloudinary": "^1.41.0", "crypto-js": "4.2.0", "date-fns": "^2.30.0", "date-fns-tz": "^2.0.0", "inngest": "3.29.2", "moment-timezone": "^0.5.43", "path": "^0.12.7", "raw-body": "^2.5.2", "react-player": "2.13.0", "sharp": "^0.32.6", "superellipsejs": "0.0.6"}, "devDependencies": {"@mui/icons-material": "5.10.9", "@mui/lab": "5.0.0-alpha.105", "@mui/material": "5.10.11", "@trivago/prettier-plugin-sort-imports": "^4.3.0", "@types/bcryptjs": "^2.4.6", "@types/chai": "^4.3.5", "@types/jest": "^29.5.2", "@types/react": "18.3.11", "@types/react-dom": "18.3.1", "config": "3.3.8", "dotenv": "16.3.1", "jest": "^29.7.0", "prettier": "^3.0.3", "react": "18.2.0", "start-server-and-test": "^2.0.2", "tsconfig": "7.0.0", "typescript": "5.2.2"}, "browser": {"child_process": false, "crypto": false}}