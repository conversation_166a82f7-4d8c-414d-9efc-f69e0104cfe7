{"name": "@memberup/database", "version": "0.0.0", "type": "module", "scripts": {"db:status": "prisma migrate status", "db:generate": "prisma generate", "db:push": "prisma db push --skip-generate", "db:push:test": "dotenv -e ../../.env.test -- prisma db push --skip-generate"}, "main": "./index.ts", "types": "./index.ts", "dependencies": {"@planetscale/database": "^1.19.0", "@prisma/adapter-planetscale": "^5.20.0", "@prisma/client": "5.20.0", "prisma": "5.20.0"}, "devDependencies": {"dotenv-cli": "^7.4.2", "prisma": "5.20.0"}}